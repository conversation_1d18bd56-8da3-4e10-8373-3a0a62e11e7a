# frozen_string_literal: true

# == Schema Information
#
# Table name: campaign_collections
#
#  id              :bigint           not null, primary key
#  name            :string           not null
#  description     :text
#  collection_type :string           not null
#  status          :string           default("draft"), not null
#  tenant_id       :bigint           not null
#  created_by_id   :bigint           not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
# Indexes
#
#  idx_campaign_collections_created_by  (created_by_id)
#  idx_campaign_collections_tenant      (tenant_id)
#  index_campaign_collections_on_name_and_tenant_id  (name,tenant_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class CampaignCollection < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: 'User', required: true

  # Attribute declarations for enum fields
  attribute :status, :string
  attribute :collection_type, :string
  attribute :vibe_collection_type, :string

  # Associations
  has_many :campaign_collection_items, dependent: :destroy
  has_many :campaigns, through: :campaign_collection_items

  # Vibe Marketing associations
  has_many :vibe_campaign_orchestrations, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
    validates :collection_type, presence: true, inclusion: { in: %w[workflow thematic experiment sequence] }
  validates :status, presence: true, inclusion: { in: %w[draft active paused completed archived] }

  # Enums
  enum :status, { draft: 'draft', active: 'active', paused: 'paused', completed: 'completed', archived: 'archived' }
  enum :collection_type, { workflow: 'workflow', thematic: 'thematic', experiment: 'experiment', sequence: 'sequence' }

  # Vibe Marketing collection types
  enum :vibe_collection_type, { 
    emotional_journey: 'emotional_journey',
    cultural_moment: 'cultural_moment', 
    authenticity_boost: 'authenticity_boost',
    psychographic_targeting: 'psychographic_targeting'
  }, prefix: :vibe

  # Scopes
  scope :by_type, ->(type) { where(collection_type: type) }
  scope :by_status, ->(status) { where(status: status) }
  scope :recent, ->(limit = 10) { order(created_at: :desc).limit(limit) }

  # Vibe Marketing scopes
  scope :by_target_emotion, ->(emotion) { where(target_emotion: emotion) }
  scope :by_cultural_moment, ->(moment) { where(cultural_moment: moment) }
  scope :vibe_strategy_exists, -> { where.not(vibe_strategy: {}) }
  scope :with_vibe_orchestration, -> { joins(:vibe_campaign_orchestrations) }

  # Instance methods
  def campaign_count
    campaigns.count
  end

  def ordered_campaigns
    campaigns.joins(:campaign_collection_items)
             .order('campaign_collection_items.sequence_order ASC')
  end

  def next_sequence_order
    (campaign_collection_items.maximum(:sequence_order) || 0) + 1
  end

  def add_campaign(campaign, user, options = {})
    return false if campaigns.include?(campaign)
    
    campaign_collection_items.create!(
      campaign: campaign,
      added_by: user,
      sequence_order: options[:sequence_order] || next_sequence_order,
      execution_conditions: options[:execution_conditions] || {},
      success_criteria: options[:success_criteria] || {},
      notes: options[:notes]
    )
  end

  def remove_campaign(campaign)
    item = campaign_collection_items.find_by(campaign: campaign)
    item&.destroy
    reorder_sequence
  end

  def reorder_campaigns(campaign_ids)
    campaign_ids.each_with_index do |campaign_id, index|
      item = campaign_collection_items.find_by(campaign_id: campaign_id)
      item&.update!(sequence_order: index + 1)
    end
  end

  def can_start_next_campaign?
    return false unless workflow?
    
    current_campaign = ordered_campaigns.joins(:campaign_collection_items)
                                       .where('campaign_collection_items.is_completed = false')
                                       .first
    current_campaign.nil? || current_campaign.completed?
  end

  def start_next_campaign!
    return false unless can_start_next_campaign?
    
    next_item = campaign_collection_items.where(is_completed: false)
                                       .order(:sequence_order)
                                       .first
    
    if next_item
      next_item.campaign.update!(status: 'active')
      next_item.update!(started_at: Time.current)
      true
    else
      false
    end
  end

  def progress_percentage
    return 0 if campaign_collection_items.count.zero?
    
    completed_count = campaign_collection_items.where(is_completed: true).count
    (completed_count.to_f / campaign_collection_items.count * 100).round
  end

  def type_color
    case collection_type
    when 'workflow' then 'blue'
    when 'thematic' then 'green'
    when 'experiment' then 'yellow'
    when 'sequence' then 'purple'
    else 'gray'
    end
  end

  def status_color
    case status
    when 'draft' then 'gray'
    when 'active' then 'green'
    when 'paused' then 'yellow'
    when 'completed' then 'blue'
    when 'archived' then 'red'
    else 'gray'
    end
  end

  # Vibe Marketing methods
  def overall_vibe_score
    return 0.0 if campaigns.empty?
    
    scores = campaigns.map(&:vibe_score).compact
    return 0.0 if scores.empty?
    
    scores.sum / scores.length
  end

  def emotional_consistency_score
    return 100.0 if campaigns.count <= 1 || target_emotion.blank?
    
    matching_campaigns = campaigns.where(emotional_tone: target_emotion).count
    (matching_campaigns.to_f / campaigns.count * 100).round(1)
  end

  def cultural_alignment_strength
    return 0.0 if cultural_moment.blank? || campaigns.empty?
    
    aligned_campaigns = campaigns.select { |c| c.cultural_moment_alignment.present? }
    return 0.0 if aligned_campaigns.empty?
    
    total_alignment = aligned_campaigns.sum { |c| c.cultural_moment_alignment[:alignment_percentage] }
    (total_alignment / aligned_campaigns.count).round(1)
  end

  def authenticity_validation_status
    return 'pending' if campaigns.empty?
    
    validations = campaigns.map(&:vibe_validation_status)
    
    if validations.all? { |v| v == 'validated' }
      'all_validated'
    elsif validations.any? { |v| v == 'failed_validation' }
      'validation_failed'
    elsif validations.any? { |v| v == 'needs_review' }
      'needs_review'
    else
      'under_review'
    end
  end

  def vibe_strategy_type
    return nil if vibe_strategy.blank?
    
    vibe_strategy.dig('strategy_type') || detect_strategy_type
  end

  def emotional_journey_progression
    return {} unless vibe_strategy.dig('emotional_journey').present?
    
    journey_stages = vibe_strategy.dig('emotional_journey', 'stages') || []
    current_stage_index = vibe_strategy.dig('emotional_journey', 'current_stage_index') || 0
    
    {
      stages: journey_stages,
      current_stage_index: current_stage_index,
      current_stage: journey_stages[current_stage_index],
      progress_percentage: journey_stages.empty? ? 0 : ((current_stage_index + 1).to_f / journey_stages.length * 100).round(1),
      next_stage: journey_stages[current_stage_index + 1]
    }
  end

  def psychographic_targeting_profile
    return {} unless vibe_strategy.dig('psychographic_profile').present?
    
    vibe_strategy['psychographic_profile']
  end

  def cultural_moment_details
    return nil if cultural_moment.blank?
    
    moment = CulturalMoment.active.find_by(title: cultural_moment)
    return nil unless moment
    
    {
      title: moment.title,
      description: moment.description,
      category: moment.category,
      relevance_score: moment.relevance_score,
      target_demographics: moment.target_demographics,
      keywords: moment.keywords,
      date_range: {
        start: moment.start_date,
        end: moment.end_date
      }
    }
  end

  def vibe_orchestration_status
    return 'none' if vibe_campaign_orchestrations.empty?
    
    latest_orchestration = vibe_campaign_orchestrations.order(created_at: :desc).first
    {
      type: latest_orchestration.orchestration_type,
      status: latest_orchestration.status,
      progress: latest_orchestration.progress_percentage,
      started_at: latest_orchestration.started_at,
      completed_at: latest_orchestration.completed_at,
      results: latest_orchestration.results
    }
  end

  def can_start_vibe_orchestration?
    campaigns.any? && vibe_strategy.present? && 
    (vibe_campaign_orchestrations.empty? || vibe_campaign_orchestrations.none? { |o| o.status.in?(['pending', 'running']) })
  end

  def recommended_emotional_tone
    return target_emotion if target_emotion.present?
    
    # Analyze campaigns to suggest optimal emotional tone
    campaign_tones = campaigns.where.not(emotional_tone: nil).pluck(:emotional_tone)
    return nil if campaign_tones.empty?
    
    # Return most common tone or suggest based on collection type
    campaign_tones.group_by(&:itself).max_by { |_tone, instances| instances.count }&.first
  end

  def vibe_health_score
    factors = {
      overall_vibe: overall_vibe_score * 0.3,
      emotional_consistency: emotional_consistency_score * 0.25,
      cultural_alignment: cultural_alignment_strength * 0.25,
      authenticity_status: authenticity_score_factor * 0.2
    }
    
    factors.values.sum.round(1)
  end

  def needs_vibe_optimization?
    vibe_health_score < 70.0 || authenticity_validation_status != 'all_validated'
  end

  private

  def reorder_sequence
    campaign_collection_items.order(:sequence_order).each_with_index do |item, index|
      item.update_column(:sequence_order, index + 1)
    end
  end

  # Vibe Marketing private methods
  def detect_strategy_type
    return 'emotional_journey' if target_emotion.present? && vibe_strategy.dig('emotional_journey').present?
    return 'cultural_moment' if cultural_moment.present?
    return 'psychographic_targeting' if vibe_strategy.dig('psychographic_profile').present?
    return 'authenticity_boost' if vibe_strategy.dig('authenticity_focus').present?
    
    'general'
  end

  def authenticity_score_factor
    case authenticity_validation_status
    when 'all_validated' then 100.0
    when 'under_review' then 70.0
    when 'needs_review' then 40.0
    when 'validation_failed' then 10.0
    else 50.0
    end
  end
end
