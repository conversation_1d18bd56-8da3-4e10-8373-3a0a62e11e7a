# frozen_string_literal: true

# == Schema Information
#
# Table name: campaign_collections
#
#  id              :bigint           not null, primary key
#  name            :string           not null
#  description     :text
#  collection_type :string           not null
#  status          :string           default("draft"), not null
#  tenant_id       :bigint           not null
#  created_by_id   :bigint           not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
# Indexes
#
#  idx_campaign_collections_created_by  (created_by_id)
#  idx_campaign_collections_tenant      (tenant_id)
#  index_campaign_collections_on_name_and_tenant_id  (name,tenant_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class CampaignCollection < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: 'User', required: true

  # Associations
  has_many :campaign_collection_items, dependent: :destroy
  has_many :campaigns, through: :campaign_collection_items

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :collection_type, presence: true, inclusion: { in: %w[workflow thematic experiment sequence] }
  validates :status, presence: true, inclusion: { in: %w[draft active paused completed archived] }

  # Enums
  enum status: { draft: 'draft', active: 'active', paused: 'paused', completed: 'completed', archived: 'archived' }
  enum collection_type: { workflow: 'workflow', thematic: 'thematic', experiment: 'experiment', sequence: 'sequence' }

  # Scopes
  scope :by_type, ->(type) { where(collection_type: type) }
  scope :by_status, ->(status) { where(status: status) }
  scope :recent, ->(limit = 10) { order(created_at: :desc).limit(limit) }

  # Instance methods
  def campaign_count
    campaigns.count
  end

  def ordered_campaigns
    campaigns.joins(:campaign_collection_items)
             .order('campaign_collection_items.sequence_order ASC')
  end

  def next_sequence_order
    (campaign_collection_items.maximum(:sequence_order) || 0) + 1
  end

  def add_campaign(campaign, user, options = {})
    return false if campaigns.include?(campaign)
    
    campaign_collection_items.create!(
      campaign: campaign,
      added_by: user,
      sequence_order: options[:sequence_order] || next_sequence_order,
      execution_conditions: options[:execution_conditions] || {},
      success_criteria: options[:success_criteria] || {},
      notes: options[:notes]
    )
  end

  def remove_campaign(campaign)
    item = campaign_collection_items.find_by(campaign: campaign)
    item&.destroy
    reorder_sequence
  end

  def reorder_campaigns(campaign_ids)
    campaign_ids.each_with_index do |campaign_id, index|
      item = campaign_collection_items.find_by(campaign_id: campaign_id)
      item&.update!(sequence_order: index + 1)
    end
  end

  def can_start_next_campaign?
    return false unless workflow?
    
    current_campaign = ordered_campaigns.joins(:campaign_collection_items)
                                       .where('campaign_collection_items.is_completed = false')
                                       .first
    current_campaign.nil? || current_campaign.completed?
  end

  def start_next_campaign!
    return false unless can_start_next_campaign?
    
    next_item = campaign_collection_items.where(is_completed: false)
                                       .order(:sequence_order)
                                       .first
    
    if next_item
      next_item.campaign.update!(status: 'active')
      next_item.update!(started_at: Time.current)
      true
    else
      false
    end
  end

  def progress_percentage
    return 0 if campaign_collection_items.count.zero?
    
    completed_count = campaign_collection_items.where(is_completed: true).count
    (completed_count.to_f / campaign_collection_items.count * 100).round
  end

  def type_color
    case collection_type
    when 'workflow' then 'blue'
    when 'thematic' then 'green'
    when 'experiment' then 'yellow'
    when 'sequence' then 'purple'
    else 'gray'
    end
  end

  def status_color
    case status
    when 'draft' then 'gray'
    when 'active' then 'green'
    when 'paused' then 'yellow'
    when 'completed' then 'blue'
    when 'archived' then 'red'
    else 'gray'
    end
  end

  private

  def reorder_sequence
    campaign_collection_items.order(:sequence_order).each_with_index do |item, index|
      item.update_column(:sequence_order, index + 1)
    end
  end
end
