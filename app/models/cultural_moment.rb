# frozen_string_literal: true

# == Schema Information
#
# Table name: cultural_moments
#
#  id                  :bigint           not null, primary key
#  tenant_id           :bigint           not null
#  title               :string           not null
#  description         :text
#  moment_type         :string           not null
#  category            :string
#  start_date          :date
#  end_date            :date
#  relevance_score     :integer          default(0)
#  target_demographics :jsonb            default({}), not null
#  engagement_metrics  :jsonb            default({}), not null
#  keywords            :jsonb            default([]), not null
#  status              :string           default("active")
#  created_by_id       :bigint           not null
#  created_at          :datetime         not null
#  updated_at          :datetime         not null
#
# Indexes
#
#  index_cultural_moments_on_end_date                      (end_date)
#  index_cultural_moments_on_keywords                      (keywords) USING gin
#  index_cultural_moments_on_relevance_score               (relevance_score)
#  index_cultural_moments_on_start_date                    (start_date)
#  index_cultural_moments_on_target_demographics           (target_demographics) USING gin
#  index_cultural_moments_on_tenant_id_and_moment_type     (tenant_id,moment_type)
#  index_cultural_moments_on_tenant_id_and_status          (tenant_id,status)
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class CulturalMoment < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: 'User', required: true

  # Validations
  validates :title, presence: true
  validates :moment_type, presence: true, inclusion: { 
    in: %w[trend event holiday movement viral]
  }
  validates :status, presence: true, inclusion: { 
    in: %w[active expired archived]
  }
  validates :relevance_score, numericality: { 
    greater_than_or_equal_to: 0, 
    less_than_or_equal_to: 100 
  }
  
  # Date validations
  validate :end_date_after_start_date, if: -> { start_date.present? && end_date.present? }

  # Attribute declarations for enum fields
  attribute :moment_type, :string
  attribute :status, :string

  # Enums
  enum :moment_type, {
    trend: 'trend',
    event: 'event', 
    holiday: 'holiday',
    movement: 'movement',
    viral: 'viral'
  }

  enum :status, {
    active: 'active',
    expired: 'expired',
    archived: 'archived'
  }

  # Scopes
  scope :by_type, ->(type) { where(moment_type: type) }
  scope :by_status, ->(status) { where(status: status) }
  scope :by_category, ->(category) { where(category: category) }
  scope :current, -> { where('start_date <= ? AND (end_date IS NULL OR end_date >= ?)', Date.current, Date.current) }
  scope :upcoming, -> { where('start_date > ?', Date.current) }
  scope :past, -> { where('end_date < ?', Date.current) }
  scope :high_relevance, -> { where('relevance_score >= ?', 75) }
  scope :recent, -> { order(created_at: :desc) }

  # Instance methods
  def current?
    return false unless start_date.present?
    
    start_condition = start_date <= Date.current
    end_condition = end_date.nil? || end_date >= Date.current
    
    start_condition && end_condition
  end

  def upcoming?
    start_date.present? && start_date > Date.current
  end

  def past?
    end_date.present? && end_date < Date.current
  end

  def duration_in_days
    return nil unless start_date.present?
    return nil if end_date.blank?
    
    (end_date - start_date).to_i + 1
  end

  def days_until_start
    return 0 unless start_date.present?
    return 0 if start_date <= Date.current
    
    (start_date - Date.current).to_i
  end

  def days_remaining
    return nil unless end_date.present?
    return 0 if end_date < Date.current
    
    (end_date - Date.current).to_i
  end

  def progress_percentage
    return 100 if past?
    return 0 if upcoming?
    return 50 unless start_date.present? && end_date.present?
    
    total_duration = duration_in_days
    return 100 if total_duration <= 0
    
    elapsed_days = (Date.current - start_date).to_i + 1
    ((elapsed_days.to_f / total_duration) * 100).round(1)
  end

  def peak_engagement_period?
    return false unless current?
    
    # Peak is considered the middle 50% of the moment's duration
    return true if duration_in_days.nil? || duration_in_days <= 2
    
    progress = progress_percentage
    progress.between?(25, 75)
  end

  def relevance_level
    case relevance_score
    when 90..100 then 'critical'
    when 75..89 then 'high'
    when 50..74 then 'moderate'
    when 25..49 then 'low'
    else 'minimal'
    end
  end

  def primary_demographic
    return nil if target_demographics.blank?
    
    demographics = target_demographics.dup
    demographics.max_by { |_demo, weight| weight.to_i }&.first
  end

  def keyword_list
    return [] if keywords.blank?
    
    keywords.is_a?(Array) ? keywords : [keywords].flatten
  end

  def trending_score
    base_score = relevance_score
    
    # Boost score for current moments
    base_score += 20 if current?
    
    # Reduce score for past moments
    base_score -= 30 if past?
    
    # Boost for viral content
    base_score += 15 if viral?
    
    # Cap at 100
    [base_score, 100].min
  end

  def engagement_summary
    return {} if engagement_metrics.blank?
    
    {
      total_mentions: engagement_metrics.dig('mentions') || 0,
      sentiment_score: engagement_metrics.dig('sentiment') || 0,
      reach_estimate: engagement_metrics.dig('reach') || 0,
      trending_platforms: engagement_metrics.dig('platforms') || []
    }
  end

  def marketing_opportunity_score
    score = relevance_score * 0.4
    
    # Time-based factors
    if current?
      score += 30
    elsif upcoming? && days_until_start <= 7
      score += 20
    elsif past?
      score -= 40
    end
    
    # Engagement factors
    engagement = engagement_summary
    if engagement[:total_mentions].to_i > 1000
      score += 15
    end
    
    if engagement[:sentiment_score].to_f > 0.7
      score += 10
    end
    
    [score, 100].min.round(1)
  end

  def similar_moments
    return CulturalMoment.none if category.blank?
    
    self.class.where(category: category)
              .where.not(id: id)
              .where(tenant: tenant)
              .limit(5)
  end

  def campaign_alignment_suggestions
    suggestions = []
    
    if current? && high_relevance?
      suggestions << "Consider launching campaigns now to capitalize on peak relevance"
    end
    
    if upcoming? && days_until_start <= 14
      suggestions << "Prepare campaigns to launch #{days_until_start} days from now"
    end
    
    if peak_engagement_period?
      suggestions << "Prime time for maximum engagement - consider increasing ad spend"
    end
    
    demographic = primary_demographic
    if demographic.present?
      suggestions << "Target #{demographic.humanize} demographic for best alignment"
    end
    
    suggestions
  end

  def status_color
    case status
    when 'active' then current? ? 'green' : 'blue'
    when 'expired' then 'red'
    when 'archived' then 'gray'
    else 'gray'
    end
  end

  def type_icon
    case moment_type
    when 'trend' then '📈'
    when 'event' then '🎉'
    when 'holiday' then '🎊'
    when 'movement' then '✊'
    when 'viral' then '🔥'
    else '📅'
    end
  end

  def self.trending_now
    current.high_relevance.order(relevance_score: :desc)
  end

  def self.upcoming_opportunities
    upcoming.where('start_date <= ?', Date.current + 30.days)
            .order(:start_date)
  end

  def self.by_marketing_opportunity
    order(Arel.sql('(relevance_score * 0.4 + CASE 
      WHEN start_date <= CURRENT_DATE AND (end_date IS NULL OR end_date >= CURRENT_DATE) THEN 30
      WHEN start_date > CURRENT_DATE AND start_date <= CURRENT_DATE + INTERVAL \'7 days\' THEN 20
      WHEN end_date < CURRENT_DATE THEN -40
      ELSE 0
    END) DESC'))
  end

  private

  def end_date_after_start_date
    return unless start_date.present? && end_date.present?
    
    if end_date <= start_date
      errors.add(:end_date, 'must be after start date')
    end
  end

  def high_relevance?
    relevance_score >= 75
  end
end
