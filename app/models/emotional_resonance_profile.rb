# frozen_string_literal: true

# EmotionalResonanceProfile - Tracks and analyzes emotional resonance patterns for campaigns
# 
# This model stores detailed emotional analysis data including:
# - Plutchik's emotion wheel mappings
# - Audience emotional response predictions
# - Resonance strength scoring
# - Demographic-specific emotional patterns
class EmotionalResonanceProfile < ApplicationRecord
  belongs_to :campaign
  belongs_to :tenant

  # Attribute declarations for enum fields
  attribute :profile_type, :string
  attribute :emotional_intensity, :string

  # Validations
  validates :emotion_wheel_data, presence: true
  validates :resonance_strength, presence: true, 
            inclusion: { in: 0.0..10.0, message: "must be between 0.0 and 10.0" }
  validates :confidence_score, presence: true,
            inclusion: { in: 0.0..1.0, message: "must be between 0.0 and 1.0" }
  validates :profile_type, presence: true

  # Enums
  enum :profile_type, {
    primary_emotion: 'primary_emotion',
    emotional_journey: 'emotional_journey', 
    demographic_specific: 'demographic_specific',
    cultural_emotional: 'cultural_emotional',
    seasonal_emotional: 'seasonal_emotional'
  }

  enum :emotional_intensity, {
    subtle: 'subtle',
    moderate: 'moderate', 
    strong: 'strong',
    intense: 'intense'
  }

  # Scopes
  scope :high_resonance, -> { where('resonance_strength >= ?', 7.0) }
  scope :confident_predictions, -> { where('confidence_score >= ?', 0.8) }
  scope :by_emotion, ->(emotion) { where("emotion_wheel_data->>'primary_emotion' = ?", emotion) }
  scope :by_demographic, ->(demo) { where("demographic_data->>'target_group' = ?", demo) }
  scope :recent_profiles, -> { where(created_at: 30.days.ago..Time.current) }

  # Plutchik's 8 primary emotions
  PLUTCHIK_PRIMARY_EMOTIONS = %w[
    joy anger fear sadness disgust surprise anticipation trust
  ].freeze

  # Emotion intensity levels in Plutchik's model
  EMOTION_INTENSITIES = {
    'joy' => %w[serenity joy ecstasy],
    'anger' => %w[annoyance anger rage],
    'fear' => %w[apprehension fear terror],
    'sadness' => %w[pensiveness sadness grief],
    'disgust' => %w[boredom disgust loathing],
    'surprise' => %w[distraction surprise amazement],
    'anticipation' => %w[interest anticipation vigilance],
    'trust' => %w[acceptance trust admiration]
  }.freeze

  # Instance Methods

  # Get the primary emotion from the emotion wheel data
  def primary_emotion
    emotion_wheel_data&.dig('primary_emotion')
  end

  # Get secondary emotions that complement the primary
  def secondary_emotions
    emotion_wheel_data&.dig('secondary_emotions') || []
  end

  # Calculate emotional complexity score (0-10)
  def emotional_complexity_score
    return 0.0 unless emotion_wheel_data.present?
    
    primary_weight = 5.0
    secondary_count = secondary_emotions.size
    intensity_factor = emotional_intensity_multiplier
    
    base_score = primary_weight + (secondary_count * 1.5)
    complexity = base_score * intensity_factor
    
    [complexity, 10.0].min.round(2)
  end

  # Get target audience emotional preferences
  def audience_emotional_preferences
    demographic_data&.dig('emotional_preferences') || {}
  end

  # Calculate demographic alignment score
  def demographic_alignment_score
    return 0.0 unless demographic_data.present? && emotion_wheel_data.present?
    
    target_emotions = audience_emotional_preferences.dig('preferred_emotions') || []
    current_emotion = primary_emotion
    
    return 8.0 if target_emotions.include?(current_emotion)
    
    # Check for complementary emotions
    complementary_score = calculate_complementary_emotion_score(current_emotion, target_emotions)
    [complementary_score, 10.0].min.round(2)
  end

  # Predict emotional response strength for specific demographics
  def predict_emotional_response(demographic_segment)
    return 0.0 unless demographic_data.present?
    
    segment_data = demographic_data.dig('segments', demographic_segment) || {}
    base_resonance = segment_data.dig('expected_resonance') || resonance_strength
    
    # Adjust for cultural factors
    cultural_factor = segment_data.dig('cultural_alignment') || 1.0
    adjusted_resonance = base_resonance * cultural_factor
    
    [adjusted_resonance, 10.0].min.round(2)
  end

  # Check if emotion profile is suitable for specific campaign type
  def suitable_for_campaign_type?(campaign_type)
    suitable_emotions = campaign_type_emotion_mapping[campaign_type.to_s] || []
    suitable_emotions.include?(primary_emotion)
  end

  # Get recommended complementary emotions
  def recommended_complementary_emotions
    return [] unless primary_emotion.present?
    
    emotion_combinations[primary_emotion] || []
  end

  # Calculate emotional journey progression score
  def emotional_journey_score
    return 0.0 unless profile_type == 'emotional_journey'
    
    journey_data = emotion_wheel_data&.dig('journey_progression') || {}
    start_emotion = journey_data.dig('start_emotion')
    end_emotion = journey_data.dig('end_emotion')
    
    return 0.0 unless start_emotion && end_emotion
    
    transition_strength = calculate_emotion_transition_strength(start_emotion, end_emotion)
    journey_clarity = journey_data.dig('clarity_score') || 5.0
    
    ((transition_strength + journey_clarity) / 2).round(2)
  end

  # Get seasonal emotional relevance
  def seasonal_relevance_score
    return 0.0 unless profile_type == 'seasonal_emotional'
    
    current_season = determine_current_season
    season_emotions = seasonal_emotion_mapping[current_season] || []
    
    return 8.0 if season_emotions.include?(primary_emotion)
    
    # Partial match scoring
    secondary_match = (secondary_emotions & season_emotions).any?
    secondary_match ? 5.0 : 2.0
  end

  # Export emotional profile for external analysis
  def export_emotional_profile
    {
      profile_id: id,
      campaign_id: campaign_id,
      primary_emotion: primary_emotion,
      secondary_emotions: secondary_emotions,
      resonance_strength: resonance_strength,
      confidence_score: confidence_score,
      complexity_score: emotional_complexity_score,
      demographic_alignment: demographic_alignment_score,
      journey_score: emotional_journey_score,
      seasonal_relevance: seasonal_relevance_score,
      created_at: created_at,
      analysis_metadata: {
        emotion_wheel_data: emotion_wheel_data,
        demographic_data: demographic_data,
        profile_type: profile_type,
        emotional_intensity: emotional_intensity
      }
    }
  end

  private

  # Calculate emotional intensity multiplier
  def emotional_intensity_multiplier
    case emotional_intensity
    when 'subtle' then 0.7
    when 'moderate' then 1.0
    when 'strong' then 1.3
    when 'intense' then 1.6
    else 1.0
    end
  end

  # Calculate score for complementary emotions
  def calculate_complementary_emotion_score(current_emotion, target_emotions)
    return 0.0 unless current_emotion.present? && target_emotions.any?
    
    complementary_emotions = emotion_combinations[current_emotion] || []
    matches = target_emotions & complementary_emotions
    
    case matches.size
    when 0 then 2.0
    when 1 then 5.0
    when 2..3 then 7.0
    else 6.0
    end
  end

  # Calculate transition strength between emotions
  def calculate_emotion_transition_strength(start_emotion, end_emotion)
    return 0.0 unless start_emotion && end_emotion
    return 10.0 if start_emotion == end_emotion
    
    # Use Plutchik's emotion wheel adjacency
    adjacency_score = emotion_adjacency_score(start_emotion, end_emotion)
    intensity_compatibility = intensity_transition_score(start_emotion, end_emotion)
    
    ((adjacency_score + intensity_compatibility) / 2).round(2)
  end

  # Score emotion adjacency on Plutchik's wheel
  def emotion_adjacency_score(emotion1, emotion2)
    wheel_positions = {
      'joy' => 0, 'trust' => 1, 'fear' => 2, 'surprise' => 3,
      'sadness' => 4, 'disgust' => 5, 'anger' => 6, 'anticipation' => 7
    }
    
    pos1 = wheel_positions[emotion1] || 0
    pos2 = wheel_positions[emotion2] || 0
    
    distance = [(pos1 - pos2).abs, 8 - (pos1 - pos2).abs].min
    
    case distance
    when 0 then 10.0
    when 1 then 8.0
    when 2 then 6.0
    when 3 then 4.0
    when 4 then 2.0
    else 1.0
    end
  end

  # Score intensity transition compatibility
  def intensity_transition_score(emotion1, emotion2)
    # Transitions work better between similar intensity levels
    # or when building up intensity
    return 8.0 # Simplified for now, can be enhanced based on intensity data
  end

  # Determine current season for seasonal relevance
  def determine_current_season
    month = Time.current.month
    case month
    when 12, 1, 2 then 'winter'
    when 3, 4, 5 then 'spring'
    when 6, 7, 8 then 'summer'
    when 9, 10, 11 then 'autumn'
    end
  end

  # Mapping of campaign types to suitable emotions
  def campaign_type_emotion_mapping
    {
      'product_launch' => %w[anticipation joy surprise],
      'brand_awareness' => %w[trust joy anticipation],
      'seasonal_promotion' => %w[joy anticipation surprise],
      'crisis_response' => %w[trust sadness fear],
      'community_building' => %w[trust joy anticipation],
      'educational' => %w[trust anticipation joy],
      'entertainment' => %w[joy surprise anticipation],
      'social_cause' => %w[anger sadness trust anticipation]
    }
  end

  # Plutchik's emotion combinations
  def emotion_combinations
    {
      'joy' => %w[trust anticipation],
      'trust' => %w[joy fear],
      'fear' => %w[trust surprise],
      'surprise' => %w[fear sadness],
      'sadness' => %w[surprise disgust],
      'disgust' => %w[sadness anger],
      'anger' => %w[disgust anticipation],
      'anticipation' => %w[anger joy]
    }
  end

  # Seasonal emotion mapping
  def seasonal_emotion_mapping
    {
      'spring' => %w[joy anticipation trust],
      'summer' => %w[joy anticipation surprise],
      'autumn' => %w[sadness anticipation trust],
      'winter' => %w[trust sadness fear]
    }
  end
end
