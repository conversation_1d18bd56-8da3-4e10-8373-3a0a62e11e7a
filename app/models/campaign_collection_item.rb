# frozen_string_literal: true

# == Schema Information
#
# Table name: campaign_collection_items
#
#  id                     :bigint           not null, primary key
#  campaign_collection_id :bigint           not null
#  campaign_id            :bigint           not null
#  added_by_id            :bigint           not null
#  sequence_order         :integer          default(0), not null
#  execution_conditions   :jsonb            default({}), not null
#  success_criteria       :jsonb            default({}), not null
#  notes                  :text
#  is_completed           :boolean          default(false), not null
#  started_at             :datetime
#  completed_at           :datetime
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  idx_campaign_collection_items_added_by     (added_by_id)
#  idx_campaign_collection_items_campaign     (campaign_id)
#  idx_campaign_collection_items_collection   (campaign_collection_id)
#  idx_campaign_collection_items_completed    (is_completed)
#  idx_campaign_collection_items_sequence     (sequence_order)
#  idx_campaign_collection_items_unique       (campaign_collection_id,campaign_id) UNIQUE
#
# Foreign Keys
#
#  fk_rails_...  (added_by_id => users.id)
#  fk_rails_...  (campaign_collection_id => campaign_collections.id)
#  fk_rails_...  (campaign_id => campaigns.id)
#

class CampaignCollectionItem < ApplicationRecord
  belongs_to :campaign_collection, required: true
  belongs_to :campaign, required: true
  belongs_to :added_by, class_name: 'User', required: true

  # Validations
  validates :campaign_collection_id, uniqueness: { scope: :campaign_id }
  validates :sequence_order, presence: true, numericality: { greater_than_or_equal_to: 0 }

  # Scopes
  scope :ordered, -> { order(:sequence_order) }
  scope :completed, -> { where(is_completed: true) }
  scope :pending, -> { where(is_completed: false) }
  scope :by_user, ->(user) { where(added_by: user) }

  # Callbacks
  before_validation :set_sequence_order, if: -> { sequence_order.blank? }

  # Instance methods
  def tenant
    campaign_collection.tenant
  end

  def mark_completed!
    update!(
      is_completed: true,
      completed_at: Time.current
    )
  end

  def mark_started!
    update!(started_at: Time.current) unless started_at.present?
  end

  def duration
    return nil unless started_at.present?
    
    end_time = completed_at || Time.current
    ((end_time - started_at) / 1.hour).round(2)
  end

  def can_start?
    return true unless campaign_collection.workflow?
    
    # For workflow collections, check if previous items are completed
    previous_items = campaign_collection.campaign_collection_items
                                      .where('sequence_order < ?', sequence_order)
    
    previous_items.all?(&:is_completed?)
  end

  def execution_conditions_met?
    return true if execution_conditions.blank?
    
    # Check various execution conditions
    execution_conditions.all? do |condition, value|
      case condition
      when 'budget_threshold'
        campaign.budget_in_dollars >= value.to_f
      when 'previous_campaign_performance'
        check_previous_performance(value)
      when 'date_constraint'
        check_date_constraint(value)
      else
        true
      end
    end
  end

  def success_criteria_met?
    return false unless campaign.completed?
    return true if success_criteria.blank?
    
    # Check success criteria against campaign metrics
    success_criteria.all? do |criteria, target|
      case criteria
      when 'min_conversions'
        campaign.total_conversions >= target.to_i
      when 'min_revenue'
        campaign.total_revenue >= target.to_f
      when 'min_ctr'
        campaign.average_ctr >= target.to_f
      else
        true
      end
    end
  end

  private

  def set_sequence_order
    self.sequence_order = campaign_collection.next_sequence_order if campaign_collection.present?
  end

  def check_previous_performance(criteria)
    # Implement logic to check previous campaign performance
    # This could check metrics like CTR, conversion rate, etc.
    true
  end

  def check_date_constraint(constraint)
    # Implement date-based constraints
    case constraint['type']
    when 'after_date'
      Date.current >= constraint['date'].to_date
    when 'before_date'
      Date.current <= constraint['date'].to_date
    else
      true
    end
  end
end
