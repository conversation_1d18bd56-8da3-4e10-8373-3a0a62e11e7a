# frozen_string_literal: true

# == Schema Information
# Table name: campaigns
#
#  id              :bigint           not null, primary key
#  name            :string           not null
#  description     :text
#  campaign_type   :integer          default(0), not null
#  status          :integer          default(0), not null
#  target_audience :string           not null
#  start_date      :date
#  end_date        :date
#  budget_cents    :integer          default(0)
#  settings        :jsonb            default({}), not null
#  tenant_id       :bigint           not null
#  created_by_id   :bigint           not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
# Indexes
#
#  index_campaigns_on_campaign_type        (campaign_type)
#  index_campaigns_on_created_by_id        (created_by_id)
#  index_campaigns_on_name_and_tenant_id   (name,tenant_id) UNIQUE
#  index_campaigns_on_settings             (settings) USING gin
#  index_campaigns_on_start_date           (start_date)
#  index_campaigns_on_status               (status)
#  index_campaigns_on_tenant_id            (tenant_id)
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class Campaign < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: 'User', required: true

  # Specialized campaign associations
  has_one :email_campaign, dependent: :destroy
  has_one :social_campaign, dependent: :destroy
  has_one :seo_campaign, dependent: :destroy

  # Metrics association
  has_many :campaign_metrics, dependent: :destroy

  # AI Agent Workflows
  has_many :agent_workflows, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :campaign_type, presence: true
  validates :status, presence: true
  validates :target_audience, presence: true
  validates :budget_cents, numericality: { greater_than_or_equal_to: 0 }
  
  # Date validations
  validate :end_date_after_start_date, if: -> { start_date.present? && end_date.present? }

  # Enums
  enum :status, { 
    draft: 0, 
    active: 1, 
    paused: 2, 
    completed: 3, 
    cancelled: 4 
  }

  enum :campaign_type, { 
    email: 0, 
    social: 1, 
    seo: 2, 
    multi_channel: 3 
  }

  # Scopes
  scope :by_status, ->(status) { where(status: status) }
  scope :by_type, ->(type) { where(campaign_type: type) }
  scope :recent, -> { order(created_at: :desc) }
  scope :ongoing, -> { where(status: [:active, :paused]) }

  # Instance methods
  def can_be_activated?
    draft? || paused?
  end

  def duration_in_days
    return nil unless start_date.present? && end_date.present?
    (end_date - start_date).to_i
  end

  def progress_percentage
    return 100 if completed?
    return 0 if draft? || start_date.blank? || end_date.blank?
    
    now = Date.current
    return 0 if now < start_date
    return 100 if now > end_date
    
    total_days = duration_in_days
    return 0 if total_days <= 0
    
    elapsed_days = (now - start_date).to_i
    ((elapsed_days.to_f / total_days) * 100).round
  end

  def budget_in_dollars
    budget_cents / 100.0
  end

  def budget_in_dollars=(amount)
    self.budget_cents = (amount.to_f * 100).round
  end

  # Performance metrics calculated from campaign_metrics
  def click_through_rate
    total_impressions = campaign_metrics.sum(:impressions)
    total_clicks = campaign_metrics.sum(:clicks)
    return 0.0 if total_impressions.zero?

    (total_clicks.to_f / total_impressions * 100).round(2)
  end

  def conversion_rate
    total_clicks = campaign_metrics.sum(:clicks)
    total_conversions = campaign_metrics.sum(:conversions)
    return 0.0 if total_clicks.zero?

    (total_conversions.to_f / total_clicks * 100).round(2)
  end

  def total_impressions
    campaign_metrics.sum(:impressions)
  end

  def total_clicks
    campaign_metrics.sum(:clicks)
  end

  def total_conversions
    campaign_metrics.sum(:conversions)
  end

  def total_revenue
    campaign_metrics.sum(:revenue_cents) / 100.0
  end

  def total_cost
    campaign_metrics.sum(:cost_cents) / 100.0
  end

  def return_on_ad_spend
    return 0.0 if total_cost.zero?
    ((total_revenue / total_cost) * 100).round(2)
  end

  def roi
    return 0.0 if total_cost.zero?
    (((total_revenue - total_cost) / total_cost) * 100).round(2)
  end

  # AI Agent Workflow methods
  def has_active_ai_workflows?
    agent_workflows.active.exists?
  end

  def latest_ai_workflow
    agent_workflows.recent.first
  end

  def ai_content_generated?
    case campaign_type
    when 'email'
      email_campaign&.content.present? && email_campaign&.settings&.dig('ai_generated')
    when 'social'
      social_campaign&.content_variants&.present? && social_campaign&.social_settings&.dig('ai_generated')
    when 'seo'
      seo_campaign&.content_strategy&.present? && seo_campaign&.seo_settings&.dig('ai_generated')
    when 'multi_channel'
      ai_content_generated_for_any_channel?
    else
      false
    end
  end

  def ai_workflow_status
    latest_workflow = latest_ai_workflow
    return 'none' unless latest_workflow
    
    case latest_workflow.status
    when 'running', 'pending'
      'generating'
    when 'completed'
      'completed'
    when 'failed'
      'error'
    when 'cancelled'
      'cancelled'
    else
      'unknown'
    end
  end

  def can_generate_ai_content?
    draft? && !has_active_ai_workflows?
  end

  def ai_optimization_available?
    (active? || completed?) && !has_active_ai_workflows? && campaign_metrics.any?
  end

  def channels
    case campaign_type
    when 'email'
      ['Email']
    when 'social'
      social_campaign&.platforms&.map(&:titleize) || ['Social Media']
    when 'seo'
      ['Search Engine']
    when 'multi_channel'
      ['Email', 'Social Media', 'Search Engine']
    else
      ['Multiple channels']
    end
  end

  private

  def ai_content_generated_for_any_channel?
    [
      email_campaign&.settings&.dig('ai_generated'),
      social_campaign&.social_settings&.dig('ai_generated'),
      seo_campaign&.seo_settings&.dig('ai_generated')
    ].any?
  end

  # Convenience methods for view compatibility
  alias_method :impressions, :total_impressions
  alias_method :clicks, :total_clicks
  alias_method :conversions, :total_conversions
  alias_method :spend, :total_cost

  private

  def end_date_after_start_date
    return unless start_date.present? && end_date.present?
    
    if end_date <= start_date
      errors.add(:end_date, 'must be after start date')
    end
  end
end
