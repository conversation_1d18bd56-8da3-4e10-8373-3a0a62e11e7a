# frozen_string_literal: true

# == Schema Information
# Table name: campaigns
#
#  id              :bigint           not null, primary key
#  name            :string           not null
#  description     :text
#  campaign_type   :integer          default(0), not null
#  status          :integer          default(0), not null
#  target_audience :string           not null
#  start_date      :date
#  end_date        :date
#  budget_cents    :integer          default(0)
#  settings        :jsonb            default({}), not null
#  tenant_id       :bigint           not null
#  created_by_id   :bigint           not null
#  created_at      :datetime         not null
#  updated_at      :datetime         not null
#
# Indexes
#
#  index_campaigns_on_campaign_type        (campaign_type)
#  index_campaigns_on_created_by_id        (created_by_id)
#  index_campaigns_on_name_and_tenant_id   (name,tenant_id) UNIQUE
#  index_campaigns_on_settings             (settings) USING gin
#  index_campaigns_on_start_date           (start_date)
#  index_campaigns_on_status               (status)
#  index_campaigns_on_tenant_id            (tenant_id)
#
# Foreign Keys
#
#  fk_rails_...  (created_by_id => users.id)
#  fk_rails_...  (tenant_id => tenants.id)
#

class Campaign < ApplicationRecord
  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true
  belongs_to :created_by, class_name: 'User', required: true

  # Specialized campaign associations
  has_one :email_campaign, dependent: :destroy
  has_one :social_campaign, dependent: :destroy
  has_one :seo_campaign, dependent: :destroy

  # Metrics association
  has_many :campaign_metrics, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: { scope: :tenant_id }
  validates :campaign_type, presence: true
  validates :status, presence: true
  validates :target_audience, presence: true
  validates :budget_cents, numericality: { greater_than_or_equal_to: 0 }
  
  # Date validations
  validate :end_date_after_start_date, if: -> { start_date.present? && end_date.present? }

  # Enums
  enum :status, { 
    draft: 0, 
    active: 1, 
    paused: 2, 
    completed: 3, 
    cancelled: 4 
  }

  enum :campaign_type, { 
    email: 0, 
    social: 1, 
    seo: 2, 
    multi_channel: 3 
  }

  # Scopes
  scope :by_status, ->(status) { where(status: status) }
  scope :by_type, ->(type) { where(campaign_type: type) }
  scope :recent, -> { order(created_at: :desc) }
  scope :ongoing, -> { where(status: [:active, :paused]) }

  # Instance methods
  def can_be_activated?
    draft? || paused?
  end

  def duration_in_days
    return nil unless start_date.present? && end_date.present?
    (end_date - start_date).to_i
  end

  def progress_percentage
    return 100 if completed?
    return 0 if draft? || start_date.blank? || end_date.blank?
    
    now = Date.current
    return 0 if now < start_date
    return 100 if now > end_date
    
    total_days = duration_in_days
    return 0 if total_days <= 0
    
    elapsed_days = (now - start_date).to_i
    ((elapsed_days.to_f / total_days) * 100).round
  end

  def budget_in_dollars
    budget_cents / 100.0
  end

  def budget_in_dollars=(amount)
    self.budget_cents = (amount.to_f * 100).round
  end

  private

  def end_date_after_start_date
    return unless start_date.present? && end_date.present?
    
    if end_date <= start_date
      errors.add(:end_date, 'must be after start date')
    end
  end
end
