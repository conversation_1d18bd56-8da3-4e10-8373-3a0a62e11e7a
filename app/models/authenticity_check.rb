# frozen_string_literal: true

# AuthenticityCheck - Validates and scores campaign authenticity using AI analysis
#
# This model provides comprehensive authenticity validation including:
# - Brand voice consistency analysis
# - Message authenticity scoring
# - Cultural sensitivity validation
# - Audience trust factor assessment
class AuthenticityCheck < ApplicationRecord
  belongs_to :campaign
  belongs_to :tenant

  # Attribute declarations for enum fields
  attribute :check_type, :string
  attribute :status, :string

  # Validations
  validates :authenticity_score, presence: true,
            inclusion: { in: 0.0..10.0, message: "must be between 0.0 and 10.0" }
  validates :check_type, presence: true
  validates :status, presence: true

  # Enums
  enum :check_type, {
    brand_voice_consistency: 'brand_voice_consistency',
    message_authenticity: 'message_authenticity',
    cultural_sensitivity: 'cultural_sensitivity',
    audience_trust_factor: 'audience_trust_factor',
    influencer_alignment: 'influencer_alignment',
    content_originality: 'content_originality',
    value_alignment: 'value_alignment'
  }

  enum :status, {
    pending: 'pending',
    analyzing: 'analyzing',
    completed: 'completed',
    flagged: 'flagged',
    requires_review: 'requires_review',
    approved: 'approved',
    rejected: 'rejected'
  }

  # Callbacks
  before_save :set_authenticity_level_from_score
  after_create :schedule_analysis

  # Scopes
  scope :high_authenticity, -> { where('authenticity_score >= ?', 7.0) }
  scope :flagged_content, -> { where(check_status: ['flagged', 'requires_review']) }
  scope :approved_content, -> { where(check_status: 'approved') }
  scope :by_check_type, ->(type) { where(check_type: type) }
  scope :confident_results, -> { where('confidence_level >= ?', 0.8) }
  scope :recent_checks, -> { where(created_at: 7.days.ago..Time.current) }

  # Constants
  AUTHENTICITY_THRESHOLDS = {
    excellent: 8.0,
    high: 6.0,
    moderate: 3.0,
    low: 0.0
  }.freeze

  BRAND_VOICE_ATTRIBUTES = %w[
    tone formality vocabulary_style personality_traits
    communication_approach emotional_expression cultural_awareness
  ].freeze

  CULTURAL_SENSITIVITY_FACTORS = %w[
    language_appropriateness cultural_references stereotype_avoidance
    inclusive_messaging respect_for_traditions awareness_of_current_events
  ].freeze

  # Instance Methods

  # Get detailed analysis results
  def analysis_details
    analysis_data&.dig('details') || {}
  end

  # Get specific authenticity factors
  def authenticity_factors
    analysis_data&.dig('factors') || {}
  end

  # Get flagged issues if any
  def flagged_issues
    analysis_data&.dig('issues') || []
  end

  # Get improvement recommendations
  def improvement_recommendations
    analysis_data&.dig('recommendations') || []
  end

  # Calculate overall brand consistency score
  def brand_consistency_score
    return 0.0 unless check_type == 'brand_voice_consistency'
    
    voice_data = analysis_details.dig('brand_voice') || {}
    consistency_scores = BRAND_VOICE_ATTRIBUTES.map do |attribute|
      voice_data.dig(attribute, 'consistency_score') || 0.0
    end
    
    return 0.0 if consistency_scores.empty?
    
    (consistency_scores.sum / consistency_scores.size).round(2)
  end

  # Calculate cultural sensitivity score
  def cultural_sensitivity_score
    return 0.0 unless check_type == 'cultural_sensitivity'
    
    cultural_data = analysis_details.dig('cultural_analysis') || {}
    sensitivity_scores = CULTURAL_SENSITIVITY_FACTORS.map do |factor|
      cultural_data.dig(factor, 'score') || 0.0
    end
    
    return 0.0 if sensitivity_scores.empty?
    
    (sensitivity_scores.sum / sensitivity_scores.size).round(2)
  end

  # Get audience trust indicators
  def audience_trust_indicators
    return {} unless check_type == 'audience_trust_factor'
    
    trust_data = analysis_details.dig('trust_analysis') || {}
    {
      transparency_score: trust_data.dig('transparency', 'score') || 0.0,
      credibility_score: trust_data.dig('credibility', 'score') || 0.0,
      relatability_score: trust_data.dig('relatability', 'score') || 0.0,
      consistency_score: trust_data.dig('consistency', 'score') || 0.0
    }
  end

  # Check if content passes authenticity threshold
  def passes_authenticity_threshold?(threshold = 6.0)
    authenticity_score >= threshold && confidence_level >= 0.7
  end

  # Get risk assessment
  def risk_assessment
    risk_data = analysis_data&.dig('risk_assessment') || {}
    
    {
      overall_risk_level: calculate_overall_risk_level,
      specific_risks: risk_data.dig('identified_risks') || [],
      mitigation_strategies: risk_data.dig('mitigation_strategies') || [],
      risk_score: risk_data.dig('risk_score') || 0.0
    }
  end

  # Calculate message originality score
  def originality_score
    return 0.0 unless check_type == 'content_originality'
    
    originality_data = analysis_details.dig('originality_analysis') || {}
    
    uniqueness_score = originality_data.dig('uniqueness_score') || 0.0
    creativity_score = originality_data.dig('creativity_score') || 0.0
    plagiarism_check = originality_data.dig('plagiarism_score') || 10.0
    
    # Higher plagiarism score is worse, so invert it
    adjusted_plagiarism = 10.0 - plagiarism_check
    
    ((uniqueness_score + creativity_score + adjusted_plagiarism) / 3).round(2)
  end

  # Get influencer alignment score
  def influencer_alignment_score
    return 0.0 unless check_type == 'influencer_alignment'
    
    alignment_data = analysis_details.dig('influencer_analysis') || {}
    
    {
      brand_values_alignment: alignment_data.dig('brand_values_score') || 0.0,
      audience_overlap_score: alignment_data.dig('audience_overlap_score') || 0.0,
      content_style_match: alignment_data.dig('content_style_score') || 0.0,
      authenticity_history: alignment_data.dig('authenticity_history_score') || 0.0
    }
  end

  # Generate comprehensive authenticity report
  def generate_authenticity_report
    {
      check_id: id,
      campaign_id: campaign_id,
      check_type: check_type,
      overall_score: authenticity_score,
      authenticity_level: authenticity_level,
      confidence_level: confidence_level,
      status: check_status,
      
      detailed_analysis: {
        brand_consistency: brand_consistency_score,
        cultural_sensitivity: cultural_sensitivity_score,
        originality: originality_score,
        trust_indicators: audience_trust_indicators,
        influencer_alignment: influencer_alignment_score
      },
      
      risk_assessment: risk_assessment,
      flagged_issues: flagged_issues,
      recommendations: improvement_recommendations,
      
      metadata: {
        analyzed_at: updated_at,
        analysis_duration: analysis_data&.dig('metadata', 'processing_time'),
        ai_model_used: analysis_data&.dig('metadata', 'ai_model'),
        analysis_version: analysis_data&.dig('metadata', 'analysis_version')
      }
    }
  end

  # Check if requires human review
  def requires_human_review?
    flagged_issues.any? || 
    authenticity_score < 4.0 || 
    confidence_level < 0.6 ||
    check_status.in?(['flagged', 'requires_review'])
  end

  # Get action items based on analysis
  def action_items
    items = []
    
    if authenticity_score < 6.0
      items << {
        priority: 'high',
        action: 'Improve overall authenticity',
        details: improvement_recommendations.first(3)
      }
    end
    
    if flagged_issues.any?
      items << {
        priority: 'critical',
        action: 'Address flagged issues',
        details: flagged_issues
      }
    end
    
    if brand_consistency_score < 7.0 && check_type == 'brand_voice_consistency'
      items << {
        priority: 'medium',
        action: 'Align with brand voice guidelines',
        details: analysis_details.dig('brand_voice', 'inconsistencies') || []
      }
    end
    
    items
  end

  # Compare with historical authenticity data
  def compare_with_historical_data
    historical_checks = campaign.authenticity_checks
                              .where.not(id: id)
                              .where(check_type: check_type)
                              .completed
                              .order(created_at: :desc)
                              .limit(5)
    
    return {} if historical_checks.empty?
    
    historical_scores = historical_checks.pluck(:authenticity_score)
    avg_historical_score = historical_scores.sum / historical_scores.size
    
    {
      current_score: authenticity_score,
      historical_average: avg_historical_score.round(2),
      trend: calculate_trend(authenticity_score, avg_historical_score),
      improvement_percentage: calculate_improvement_percentage(authenticity_score, avg_historical_score),
      historical_count: historical_scores.size
    }
  end

  private

  # Set authenticity level based on score
  def set_authenticity_level
    self.authenticity_level = case authenticity_score
                             when 8.0..10.0 then 'excellent'
                             when 6.0...8.0 then 'high'
                             when 3.0...6.0 then 'moderate'
                             else 'low'
                             end
  end

  # Schedule analysis job
  def schedule_analysis
    # This would trigger an async job in a real implementation
    # AnalyzeAuthenticityJob.perform_later(self)
  end

  # Calculate overall risk level
  def calculate_overall_risk_level
    risk_score = analysis_data&.dig('risk_assessment', 'risk_score') || 0.0
    
    case risk_score
    when 0.0..2.0 then 'low'
    when 2.0..5.0 then 'moderate'
    when 5.0..7.0 then 'high'
    else 'critical'
    end
  end

  # Calculate trend compared to historical data
  def calculate_trend(current, historical_avg)
    return 'stable' if (current - historical_avg).abs < 0.5
    
    current > historical_avg ? 'improving' : 'declining'
  end

  # Calculate improvement percentage
  def calculate_improvement_percentage(current, historical_avg)
    return 0.0 if historical_avg.zero?
    
    ((current - historical_avg) / historical_avg * 100).round(2)
  end
end
