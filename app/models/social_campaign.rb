# frozen_string_literal: true

# == Schema Information
# Table name: social_campaigns
#
#  id                   :bigint           not null, primary key
#  campaign_id          :bigint           not null
#  platforms            :jsonb            default([]), not null
#  content_variants     :jsonb            default({}), not null
#  hashtags             :string           default("")
#  target_demographics  :jsonb            default({}), not null
#  post_schedule        :jsonb            default({}), not null
#  social_settings      :jsonb            default({}), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#
# Indexes
#
#  index_social_campaigns_on_campaign_id          (campaign_id) UNIQUE
#  index_social_campaigns_on_content_variants     (content_variants) USING gin
#  index_social_campaigns_on_platforms            (platforms) USING gin
#  index_social_campaigns_on_post_schedule        (post_schedule) USING gin
#  index_social_campaigns_on_social_settings      (social_settings) USING gin
#  index_social_campaigns_on_target_demographics  (target_demographics) USING gin
#
# Foreign Keys
#
#  fk_rails_...  (campaign_id => campaigns.id)
#

class SocialCampaign < ApplicationRecord
  # Associations
  belongs_to :campaign, required: true

  # Constants
  SUPPORTED_PLATFORMS = %w[twitter linkedin facebook instagram youtube tiktok].freeze
  PLATFORM_LIMITS = {
    'twitter' => 280,
    'linkedin' => 3000,
    'facebook' => 63206,
    'instagram' => 2200,
    'youtube' => 5000,
    'tiktok' => 150
  }.freeze

  # Validations
  validates :platforms, presence: true
  validates :content_variants, presence: true
  validate :at_least_one_platform
  validate :platforms_are_supported
  validate :content_exists_for_all_platforms
  validate :content_respects_platform_limits

  # Scopes
  scope :for_platform, ->(platform) { where("platforms @> ?", [platform].to_json) }
  scope :ready_to_post, -> { 
    joins(:campaign)
      .where(campaigns: { status: 'active' })
      .where("jsonb_array_length(platforms) > 0")
      .where("content_variants != '{}'::jsonb")
  }

  # Delegate tenant access through campaign
  def tenant
    campaign.tenant
  end

  # Instance methods
  def supported_platforms
    SUPPORTED_PLATFORMS
  end

  def ready_to_post?
    return false unless campaign&.active?
    return false if platforms.empty?
    return false unless content_complete?
    return false unless valid?
    
    true
  end

  def content_for_platform(platform)
    content_variants[platform]
  end

  def character_count_for_platform(platform)
    content = content_for_platform(platform)
    content ? content.length : 0
  end

  def scheduled_posts
    post_schedule.dig('posts') || []
  end

  def hashtag_list
    return [] if hashtags.blank?
    
    hashtags.split(/\s+/).reject(&:blank?)
  end

  def estimated_reach
    follower_counts = social_settings.dig('follower_counts') || {}
    engagement_rate = social_settings.dig('engagement_rate') || 0.1
    
    total_followers = follower_counts.values.sum
    (total_followers * engagement_rate).round
  end

  def platforms_text
    platforms.join(', ').titleize
  end

  def primary_platform
    platforms.first
  end

  def multi_platform?
    platforms.length > 1
  end

  def add_scheduled_post(platform, content, scheduled_at)
    current_posts = scheduled_posts
    current_posts << {
      'platform' => platform,
      'content' => content,
      'scheduled_at' => scheduled_at.iso8601
    }
    
    self.post_schedule = post_schedule.merge('posts' => current_posts)
  end

  def remove_scheduled_post(index)
    current_posts = scheduled_posts
    current_posts.delete_at(index) if index < current_posts.length
    
    self.post_schedule = post_schedule.merge('posts' => current_posts)
  end

  private

  def at_least_one_platform
    if platforms.blank? || platforms.empty?
      errors.add(:platforms, 'must include at least one platform')
    end
  end

  def platforms_are_supported
    return if platforms.blank?
    
    unsupported = platforms - SUPPORTED_PLATFORMS
    if unsupported.any?
      errors.add(:platforms, 'contains unsupported platforms')
    end
  end

  def content_exists_for_all_platforms
    return if platforms.blank? || content_variants.blank?
    
    missing_content = platforms - content_variants.keys
    if missing_content.any?
      errors.add(:content_variants, 'must have content for all selected platforms')
    end
  end

  def content_respects_platform_limits
    return if content_variants.blank?
    
    content_variants.each do |platform, content|
      next unless PLATFORM_LIMITS[platform]
      next if content.blank?
      
      if content.length > PLATFORM_LIMITS[platform]
        errors.add(:content_variants, "#{platform.titleize} content exceeds #{PLATFORM_LIMITS[platform]} character limit")
      end
    end
  end

  def content_complete?
    return false if platforms.empty?
    return false if content_variants.empty?
    
    platforms.all? { |platform| content_variants[platform].present? }
  end
end
