# frozen_string_literal: true

# == Schema Information
# Table name: users
#
#  id                     :bigint           not null, primary key
#  email                  :string           default(""), not null
#  encrypted_password     :string           default(""), not null
#  reset_password_token   :string
#  reset_password_sent_at :datetime
#  remember_created_at    :datetime
#  confirmation_token     :string
#  confirmed_at           :datetime
#  confirmation_sent_at   :datetime
#  unconfirmed_email      :string
#  tenant_id              :bigint           not null
#  first_name             :string           not null
#  last_name              :string           not null
#  role                   :integer          default(0), not null
#  created_at             :datetime         not null
#  updated_at             :datetime         not null
#
# Indexes
#
#  index_users_on_confirmation_token         (confirmation_token) UNIQUE
#  index_users_on_email_and_tenant_id        (email,tenant_id) UNIQUE
#  index_users_on_reset_password_token       (reset_password_token) UNIQUE
#  index_users_on_role                       (role)
#  index_users_on_tenant_id                  (tenant_id)
#
# Foreign Keys
#
#  fk_rails_...  (tenant_id => tenants.id)
#

class User < ApplicationRecord
  # Devise modules - using custom validations instead of :validatable
  devise :database_authenticatable, :registerable,
         :recoverable, :rememberable, :confirmable

  # Multi-tenancy
  acts_as_tenant(:tenant)
  belongs_to :tenant, required: true

  # Validations
  validates :first_name, presence: true
  validates :last_name, presence: true
  
  # Email validations (replacing Devise's :validatable)
  validates :email, presence: true, 
                   uniqueness: { scope: :tenant_id, case_sensitive: false },
                   format: { with: URI::MailTo::EMAIL_REGEXP }
  
  # Password validations (replacing Devise's :validatable)
  validates :password, presence: true, length: { minimum: 6 }, if: :password_required?

  # Attribute declarations for enum fields
  attribute :role, :integer

  # Enums - using integer mapping for better performance
  enum :role, { member: 0, admin: 1, owner: 2 }

  # Instance methods
  def full_name
    "#{first_name} #{last_name}"
  end

  def admin_or_owner?
    admin? || owner?
  end

  private

  # Required for password validation
  def password_required?
    !persisted? || !password.nil? || !password_confirmation.nil?
  end
end
