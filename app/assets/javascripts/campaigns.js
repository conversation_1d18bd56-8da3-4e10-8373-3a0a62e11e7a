// Enhanced Campaigns Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
  initializeCampaignsPage();
});

function initializeCampaignsPage() {
  initializeViewToggle();
  initializeFilters();
  initializeSearch();
  initializeDropdowns();
  initializeStatsCards();
  initializeTour();
  initializeAnimations();
}

// View Toggle Functionality
function initializeViewToggle() {
  const viewToggleButtons = document.querySelectorAll('.campaigns-view-toggle button');
  const gridView = document.querySelector('.campaigns-grid-view');
  const tableView = document.querySelector('.campaigns-table-view');
  
  viewToggleButtons.forEach(button => {
    button.addEventListener('click', function() {
      const view = this.dataset.view;
      
      // Update active button
      viewToggleButtons.forEach(btn => btn.classList.remove('active'));
      this.classList.add('active');
      
      // Toggle views
      if (view === 'grid') {
        gridView?.classList.remove('hidden');
        tableView?.classList.add('hidden');
      } else if (view === 'table') {
        gridView?.classList.add('hidden');
        tableView?.classList.remove('hidden');
      }
      
      // Save preference
      localStorage.setItem('campaigns-view-preference', view);
      
      // Animate transition
      animateViewTransition();
    });
  });
  
  // Load saved preference
  const savedView = localStorage.getItem('campaigns-view-preference') || 'grid';
  const defaultButton = document.querySelector(`[data-view="${savedView}"]`);
  if (defaultButton) {
    defaultButton.click();
  }
}

// Enhanced Search with Keyboard Shortcuts
function initializeSearch() {
  const searchInput = document.querySelector('.campaigns-search-input');
  const searchForm = document.querySelector('.campaigns-filter-form');
  
  if (!searchInput) return;
  
  // Keyboard shortcut (Cmd+K / Ctrl+K)
  document.addEventListener('keydown', function(e) {
    if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
      e.preventDefault();
      searchInput.focus();
      searchInput.select();
    }
  });
  
  // Real-time search with debouncing
  let searchTimeout;
  searchInput.addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      // Auto-submit after 500ms of no typing
      if (this.value.length >= 3 || this.value.length === 0) {
        searchForm.submit();
      }
    }, 500);
  });
  
  // Search suggestions (future enhancement)
  searchInput.addEventListener('focus', function() {
    // Could add search suggestions dropdown here
  });
}

// Filter Management
function initializeFilters() {
  const filterSelects = document.querySelectorAll('.campaigns-filter-select');
  const quickFilterPills = document.querySelectorAll('.campaigns-filter-pill');
  const filterForm = document.querySelector('.campaigns-filter-form');
  
  // Auto-submit on filter change
  filterSelects.forEach(select => {
    select.addEventListener('change', function() {
      filterForm.submit();
    });
  });
  
  // Quick filter pills
  quickFilterPills.forEach(pill => {
    pill.addEventListener('click', function() {
      const filterType = this.dataset.filter;
      const filterValue = this.dataset.value;
      
      // Update corresponding select
      const select = document.querySelector(`[name="${filterType}"]`);
      if (select) {
        select.value = filterValue;
        filterForm.submit();
      }
    });
  });
  
  // Filter collapse/expand
  const filterToggle = document.querySelector('.campaigns-filter-toggle');
  const filterGrid = document.querySelector('.campaigns-filters-grid');
  
  if (filterToggle && filterGrid) {
    filterToggle.addEventListener('click', function() {
      filterGrid.classList.toggle('hidden');
      
      const icon = this.querySelector('svg');
      icon.style.transform = filterGrid.classList.contains('hidden') 
        ? 'rotate(0deg)' 
        : 'rotate(180deg)';
    });
  }
}

// Dropdown Menus
function initializeDropdowns() {
  const dropdownButtons = document.querySelectorAll('.campaigns-action-btn');
  
  dropdownButtons.forEach(button => {
    button.addEventListener('click', function(e) {
      e.stopPropagation();
      
      const dropdown = this.nextElementSibling;
      if (dropdown && dropdown.classList.contains('campaigns-dropdown-menu')) {
        // Close other dropdowns
        closeAllDropdowns();
        
        // Toggle current dropdown
        dropdown.classList.toggle('show');
      }
    });
  });
  
  // Close dropdowns when clicking outside
  document.addEventListener('click', function() {
    closeAllDropdowns();
  });
  
  // Prevent dropdown from closing when clicking inside
  document.querySelectorAll('.campaigns-dropdown-menu').forEach(menu => {
    menu.addEventListener('click', function(e) {
      e.stopPropagation();
    });
  });
}

function closeAllDropdowns() {
  document.querySelectorAll('.campaigns-dropdown-menu').forEach(menu => {
    menu.classList.remove('show');
  });
}

// Stats Cards Interaction
function initializeStatsCards() {
  const statCards = document.querySelectorAll('.campaigns-stat-card');
  
  statCards.forEach(card => {
    card.addEventListener('click', function() {
      const statType = this.dataset.stat;
      
      // Update URL with filter
      const url = new URL(window.location);
      if (statType && statType !== 'total') {
        url.searchParams.set('status', statType);
      } else {
        url.searchParams.delete('status');
      }
      
      // Navigate to filtered view
      window.location.href = url.toString();
    });
    
    // Add hover effect with counter animation
    card.addEventListener('mouseenter', function() {
      const number = this.querySelector('.campaigns-stat-number');
      if (number) {
        animateCounter(number);
      }
    });
  });
}

// Counter Animation
function animateCounter(element) {
  const target = parseInt(element.textContent);
  const duration = 1000;
  const start = performance.now();
  const startValue = 0;
  
  function updateCounter(currentTime) {
    const elapsed = currentTime - start;
    const progress = Math.min(elapsed / duration, 1);
    
    // Easing function
    const easeOutQuart = 1 - Math.pow(1 - progress, 4);
    const current = Math.floor(startValue + (target - startValue) * easeOutQuart);
    
    element.textContent = current;
    
    if (progress < 1) {
      requestAnimationFrame(updateCounter);
    } else {
      element.textContent = target; // Ensure exact final value
    }
  }
  
  requestAnimationFrame(updateCounter);
}

// Tour Functionality
function initializeTour() {
  const tourButton = document.querySelector('.campaigns-tour-btn');
  
  if (tourButton) {
    tourButton.addEventListener('click', function() {
      startCampaignsTour();
    });
  }
}

function startCampaignsTour() {
  // Simple tour implementation - could be enhanced with a library like Intro.js
  const tourSteps = [
    {
      element: '.campaigns-header-card',
      title: 'Campaign Center',
      content: 'Welcome to your campaign hub! Here you can see quick stats and access key actions.'
    },
    {
      element: '.campaigns-stats-grid',
      title: 'Campaign Statistics',
      content: 'Monitor your campaign performance at a glance. Click any stat card to filter campaigns.'
    },
    {
      element: '.campaigns-filters-card',
      title: 'Advanced Filters',
      content: 'Use these powerful filters to find exactly the campaigns you\'re looking for.'
    },
    {
      element: '.campaigns-create-btn',
      title: 'Create Campaign',
      content: 'Ready to launch? Click here to create your first AI-powered campaign!'
    }
  ];
  
  showTourStep(0, tourSteps);
}

function showTourStep(stepIndex, steps) {
  if (stepIndex >= steps.length) {
    return; // Tour complete
  }
  
  const step = steps[stepIndex];
  const element = document.querySelector(step.element);
  
  if (!element) {
    showTourStep(stepIndex + 1, steps);
    return;
  }
  
  // Create tour overlay
  const overlay = document.createElement('div');
  overlay.className = 'campaigns-tour-overlay';
  overlay.innerHTML = `
    <div class="campaigns-tour-backdrop"></div>
    <div class="campaigns-tour-highlight"></div>
    <div class="campaigns-tour-tooltip">
      <h4>${step.title}</h4>
      <p>${step.content}</p>
      <div class="campaigns-tour-buttons">
        <button class="campaigns-tour-skip">Skip Tour</button>
        <button class="campaigns-tour-next">${stepIndex === steps.length - 1 ? 'Finish' : 'Next'}</button>
      </div>
    </div>
  `;
  
  document.body.appendChild(overlay);
  
  // Position highlight and tooltip
  const rect = element.getBoundingClientRect();
  const highlight = overlay.querySelector('.campaigns-tour-highlight');
  const tooltip = overlay.querySelector('.campaigns-tour-tooltip');
  
  highlight.style.cssText = `
    position: fixed;
    top: ${rect.top - 8}px;
    left: ${rect.left - 8}px;
    width: ${rect.width + 16}px;
    height: ${rect.height + 16}px;
    border: 2px solid #3b82f6;
    border-radius: 8px;
    background: rgba(59, 130, 246, 0.1);
    z-index: 1001;
  `;
  
  tooltip.style.cssText = `
    position: fixed;
    top: ${rect.bottom + 16}px;
    left: ${rect.left}px;
    max-width: 320px;
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    z-index: 1002;
  `;
  
  // Event listeners
  overlay.querySelector('.campaigns-tour-skip').addEventListener('click', () => {
    document.body.removeChild(overlay);
  });
  
  overlay.querySelector('.campaigns-tour-next').addEventListener('click', () => {
    document.body.removeChild(overlay);
    showTourStep(stepIndex + 1, steps);
  });
  
  // Close on backdrop click
  overlay.querySelector('.campaigns-tour-backdrop').addEventListener('click', () => {
    document.body.removeChild(overlay);
  });
}

// Animations and Visual Effects
function initializeAnimations() {
  // Stagger animation for campaign cards
  const campaignCards = document.querySelectorAll('.campaigns-card');
  campaignCards.forEach((card, index) => {
    card.style.animationDelay = `${index * 0.1}s`;
  });
  
  // Progress bar animations
  const progressBars = document.querySelectorAll('.campaigns-progress-fill');
  progressBars.forEach(bar => {
    const width = bar.dataset.width || '0%';
    bar.style.width = '0%';
    
    setTimeout(() => {
      bar.style.width = width;
    }, 100);
  });
  
  // Intersection Observer for scroll animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };
  
  const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in');
      }
    });
  }, observerOptions);
  
  // Observe elements for scroll animations
  document.querySelectorAll('.campaigns-stat-card, .campaigns-card, .campaigns-quick-start-card').forEach(el => {
    observer.observe(el);
  });
}

function animateViewTransition() {
  const activeView = document.querySelector('.campaigns-grid-view:not(.hidden), .campaigns-table-view:not(.hidden)');
  
  if (activeView) {
    activeView.style.opacity = '0';
    activeView.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
      activeView.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
      activeView.style.opacity = '1';
      activeView.style.transform = 'translateY(0)';
    }, 50);
  }
}

// Utility Functions
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

function throttle(func, limit) {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
}

// Export for use in other modules
window.CampaignsPage = {
  initializeCampaignsPage,
  animateCounter,
  startCampaignsTour,
  closeAllDropdowns
};
