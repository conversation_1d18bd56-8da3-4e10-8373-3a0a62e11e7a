# frozen_string_literal: true

module AgentWorkflows<PERSON>elper
  def workflow_status_classes(workflow)
    case workflow.status
    when 'completed'
      'bg-green-100 text-green-600'
    when 'failed'
      'bg-red-100 text-red-600'
    when 'running'
      'bg-blue-100 text-blue-600'
    when 'pending'
      'bg-yellow-100 text-yellow-600'
    when 'cancelled'
      'bg-gray-100 text-gray-600'
    else
      'bg-gray-100 text-gray-600'
    end
  end

  def workflow_status_icon(workflow)
    case workflow.status
    when 'completed'
      content_tag(:svg, content_tag(:path, '', 
        'stroke-linecap': 'round', 'stroke-linejoin': 'round', 'stroke-width': '2',
        d: 'M5 13l4 4L19 7'),
        class: 'w-4 h-4', fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24')
    when 'failed'
      content_tag(:svg, content_tag(:path, '', 
        'stroke-linecap': 'round', 'stroke-linejoin': 'round', 'stroke-width': '2',
        d: 'M6 18L18 6M6 6l12 12'),
        class: 'w-4 h-4', fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24')
    when 'running'
      content_tag(:svg, content_tag(:path, '', 
        'stroke-linecap': 'round', 'stroke-linejoin': 'round', 'stroke-width': '2',
        d: 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15'),
        class: 'w-4 h-4 animate-spin', fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24')
    when 'pending'
      content_tag(:svg, content_tag(:path, '', 
        'stroke-linecap': 'round', 'stroke-linejoin': 'round', 'stroke-width': '2',
        d: 'M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'),
        class: 'w-4 h-4', fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24')
    when 'cancelled'
      content_tag(:svg, content_tag(:path, '', 
        'stroke-linecap': 'round', 'stroke-linejoin': 'round', 'stroke-width': '2',
        d: 'M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728'),
        class: 'w-4 h-4', fill: 'none', stroke: 'currentColor', viewBox: '0 0 24 24')
    else
      '🤖'
    end
  end

  def workflow_badge_classes(workflow)
    case workflow.status
    when 'completed'
      'bg-green-100 text-green-800'
    when 'failed'
      'bg-red-100 text-red-800'
    when 'running'
      'bg-blue-100 text-blue-800'
    when 'pending'
      'bg-yellow-100 text-yellow-800'
    when 'cancelled'
      'bg-gray-100 text-gray-800'
    else
      'bg-gray-100 text-gray-800'
    end
  end

  def workflow_description(workflow)
    case workflow.workflow_type
    when 'campaign_generation'
      'AI-generated marketing content for multiple channels'
    when 'performance_analysis'
      'AI-powered performance analysis and optimization recommendations'
    when 'multi_channel_coordination'
      'Coordinated multi-channel campaign orchestration'
    when 'content_optimization'
      'Content optimization using AI insights'
    when 'audience_segmentation'
      'AI-driven audience analysis and segmentation'
    else
      workflow.workflow_type.humanize
    end
  end

  def ai_status_indicator_classes(status)
    case status
    when 'completed'
      'bg-green-100 text-green-800 border-green-200'
    when 'generating'
      'bg-blue-100 text-blue-800 border-blue-200'
    when 'error'
      'bg-red-100 text-red-800 border-red-200'
    when 'cancelled'
      'bg-gray-100 text-gray-800 border-gray-200'
    else
      'bg-gray-100 text-gray-800 border-gray-200'
    end
  end

  def ai_status_message(campaign)
    status = campaign.ai_workflow_status
    
    case status
    when 'generating'
      "🤖 AI is generating content for your campaign..."
    when 'completed'
      "✅ AI content generation completed successfully!"
    when 'error'
      "❌ AI workflow encountered an error. Please try again."
    when 'cancelled'
      "⏸️ AI workflow was cancelled."
    when 'none'
      if campaign.ai_content_generated?
        "✨ AI content is ready! Your campaign has been enhanced."
      else
        "🚀 Ready to generate AI content for your campaign."
      end
    else
      "🤖 AI agents are ready to help optimize your campaign."
    end
  end

  def format_workflow_duration(duration)
    return 'N/A' unless duration

    if duration < 60
      "#{duration.round} seconds"
    elsif duration < 3600
      "#{(duration / 60).round} minutes"
    else
      "#{(duration / 3600).round(1)} hours"
    end
  end

  def workflow_progress_color_class(progress_percent)
    case progress_percent
    when 0..25
      'bg-red-500'
    when 26..50
      'bg-yellow-500'
    when 51..75
      'bg-blue-500'
    when 76..100
      'bg-green-500'
    else
      'bg-gray-500'
    end
  end

  def agent_type_icon(agent_type)
    case agent_type.to_s
    when 'email'
      '✉️'
    when 'social'
      '📱'
    when 'seo'
      '🔍'
    else
      '🤖'
    end
  end

  def agent_type_name(agent_type)
    case agent_type.to_s
    when 'email'
      'Email Marketing'
    when 'social'
      'Social Media'
    when 'seo'
      'SEO Content'
    else
      agent_type.to_s.humanize
    end
  end

  def content_generation_status(campaign, agent_type)
    case agent_type.to_s
    when 'email'
      campaign.email_campaign&.content.present?
    when 'social'
      campaign.social_campaign&.content_variants&.any?
    when 'seo'
      campaign.seo_campaign&.content_strategy&.any?
    else
      false
    end
  end
end
