# frozen_string_literal: true

class DashboardController < ApplicationController
  before_action :authenticate_user!
  before_action :ensure_tenant_set

  def index
    @campaigns = current_tenant.campaigns.includes(:email_campaign, :social_campaign)
                              .recent.limit(10)
    
    @campaign_stats = {
      total: current_tenant.campaigns.count,
      active: current_tenant.campaigns.active.count,
      draft: current_tenant.campaigns.draft.count,
      completed: current_tenant.campaigns.completed.count
    }
    
    @budget_stats = {
      total_budget: current_tenant.campaigns.sum(:budget_cents) / 100.0,
      active_budget: current_tenant.campaigns.active.sum(:budget_cents) / 100.0,
      spent_budget: calculate_spent_budget
    }
    
    @platform_stats = {
      email_campaigns: current_tenant.campaigns.joins(:email_campaign).count,
      social_campaigns: current_tenant.campaigns.joins(:social_campaign).count,
      multi_channel: current_tenant.campaigns.joins(:email_campaign, :social_campaign).count
    }
    
    @recent_campaigns = current_tenant.campaigns.recent.limit(5)
    @active_campaigns = current_tenant.campaigns.active.limit(3)
  end

  private

  def ensure_tenant_set
    if current_user&.tenant
      ActsAsTenant.current_tenant = current_user.tenant
    else
      redirect_to root_path, alert: "Please contact support to set up your account."
    end
  end

  def current_tenant
    current_user.tenant
  end

  def calculate_spent_budget
    current_tenant.campaigns.active.sum do |campaign|
      (campaign.budget_in_dollars * rand(0.1..0.8)).round(2)
    end
  end
end
