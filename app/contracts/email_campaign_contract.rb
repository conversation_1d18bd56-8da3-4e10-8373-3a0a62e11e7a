# frozen_string_literal: true

require 'dry/validation'

##
# Email Campaign Contract
#
# Validates email campaign input parameters using dry-validation.
# Provides comprehensive validation rules for email campaign creation and updates.
#
# @example Basic usage
#   contract = EmailCampaignContract.new
#   result = contract.call(params)
#   if result.success?
#     # Process valid data
#     email_campaign = EmailCampaign.create!(result.to_h)
#   else
#     # Handle validation errors
#     errors = result.errors.to_h
#   end
#
class EmailCampaignContract < Dry::Validation::Contract
  # Email regex pattern for validation
  EMAIL_REGEX = /\A[\w+\-.]+@[a-z\d\-]+(\.[a-z\d\-]+)*\.[a-z]+\z/i

  # Define the parameter schema
  params do
    required(:subject_line).filled(:string)
    required(:content).filled(:string)
    required(:from_name).filled(:string)
    required(:from_email).filled(:string)
    optional(:preview_text).maybe(:string)
  end

  # Custom validation rules
  rule(:subject_line) do
    key.failure('size cannot be greater than 150') if value && value.length > 150
  end

  rule(:from_email) do
    unless value && value.match?(EMAIL_REGEX)
      key.failure('must be a valid email address')
    end
  end

  rule(:preview_text) do
    if value && value.length > 200
      key.failure('size cannot be greater than 200')
    end
  end

  rule(:content) do
    if value && value.length > 50000
      key.failure('size cannot be greater than 50000 characters')
    end
  end

  rule(:from_name) do
    if value && value.length > 100
      key.failure('size cannot be greater than 100')
    end
  end
end
