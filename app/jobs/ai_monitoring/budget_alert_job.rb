# frozen_string_literal: true

##
# Budget Alert Job
#
# Sends alerts when AI usage approaches or exceeds budget thresholds.
# Handles notification delivery through multiple channels.
#
class BudgetAlertJob < ApplicationJob
  queue_as :high
  
  retry_on StandardError, wait: :exponentially_longer, attempts: 2
  
  def perform(tenant_id:, threshold:, current_percentage:, current_cost:, budget_limit:)
    tenant = Tenant.find(tenant_id)
    
    alert_data = {
      tenant: tenant,
      threshold: threshold,
      current_percentage: current_percentage.round(2),
      current_cost: current_cost.round(4),
      budget_limit: budget_limit,
      timestamp: Time.current
    }
    
    # Send notifications through configured channels
    send_email_alert(alert_data) if email_alerts_enabled?(tenant)
    send_slack_alert(alert_data) if slack_alerts_enabled?(tenant)
    send_webhook_alert(alert_data) if webhook_alerts_enabled?(tenant)
    
    # Log alert for audit trail
    log_budget_alert(alert_data)
    
    Rails.logger.warn("Budget alert sent for tenant #{tenant.name}: #{current_percentage}% of budget used")
  rescue ActiveRecord::RecordNotFound
    Rails.logger.error("Tenant not found for budget alert: #{tenant_id}")
  rescue => e
    Rails.logger.error("Failed to send budget alert: #{e.message}")
    raise
  end
  
  private
  
  def send_email_alert(alert_data)
    tenant = alert_data[:tenant]
    
    # Get admin users for the tenant
    admin_emails = tenant.users.where(role: [:admin, :owner]).pluck(:email)
    
    admin_emails.each do |email|
      BudgetAlertMailer.budget_threshold_reached(
        email: email,
        tenant_name: tenant.name,
        threshold: alert_data[:threshold],
        current_percentage: alert_data[:current_percentage],
        current_cost: alert_data[:current_cost],
        budget_limit: alert_data[:budget_limit]
      ).deliver_now
    end
  end
  
  def send_slack_alert(alert_data)
    tenant = alert_data[:tenant]
    slack_webhook_url = tenant.settings.dig('notifications', 'slack_webhook_url')
    
    return unless slack_webhook_url
    
    message = build_slack_message(alert_data)
    
    Faraday.post(slack_webhook_url) do |req|
      req.headers['Content-Type'] = 'application/json'
      req.body = { text: message }.to_json
    end
  end
  
  def send_webhook_alert(alert_data)
    tenant = alert_data[:tenant]
    webhook_url = tenant.settings.dig('notifications', 'budget_webhook_url')
    
    return unless webhook_url
    
    payload = {
      event: 'budget_threshold_reached',
      tenant_id: tenant.id,
      tenant_name: tenant.name,
      threshold: alert_data[:threshold],
      current_percentage: alert_data[:current_percentage],
      current_cost: alert_data[:current_cost],
      budget_limit: alert_data[:budget_limit],
      timestamp: alert_data[:timestamp].iso8601
    }
    
    Faraday.post(webhook_url) do |req|
      req.headers['Content-Type'] = 'application/json'
      req.body = payload.to_json
    end
  end
  
  def build_slack_message(alert_data)
    tenant = alert_data[:tenant]
    threshold = alert_data[:threshold]
    current_percentage = alert_data[:current_percentage]
    current_cost = alert_data[:current_cost]
    budget_limit = alert_data[:budget_limit]
    
    emoji = case threshold
            when 80 then '⚠️'
            when 90 then '🚨'
            when 100 then '🛑'
            else '📊'
            end
    
    urgency = threshold >= 100 ? 'CRITICAL' : 'WARNING'
    
    <<~MESSAGE
      #{emoji} **AI Budget Alert - #{urgency}**
      
      **Tenant:** #{tenant.name}
      **Current Usage:** #{current_percentage}% of monthly budget
      **Amount Spent:** $#{current_cost}
      **Budget Limit:** $#{budget_limit}
      **Threshold:** #{threshold}%
      
      #{threshold >= 100 ? 'Budget limit has been exceeded!' : "Approaching budget limit (#{threshold}% threshold reached)"}
      
      Please review AI usage and consider adjusting budget or usage patterns.
    MESSAGE
  end
  
  def log_budget_alert(alert_data)
    tenant = alert_data[:tenant]
    
    # Store alert in database for audit trail
    AlertLog.create!(
      tenant: tenant,
      alert_type: 'budget_threshold',
      severity: alert_data[:threshold] >= 100 ? 'critical' : 'warning',
      message: "Budget threshold #{alert_data[:threshold]}% reached",
      metadata: {
        threshold: alert_data[:threshold],
        current_percentage: alert_data[:current_percentage],
        current_cost: alert_data[:current_cost],
        budget_limit: alert_data[:budget_limit]
      }
    )
  end
  
  def email_alerts_enabled?(tenant)
    tenant.settings.dig('notifications', 'email_enabled') != false
  end
  
  def slack_alerts_enabled?(tenant)
    tenant.settings.dig('notifications', 'slack_enabled') == true &&
      tenant.settings.dig('notifications', 'slack_webhook_url').present?
  end
  
  def webhook_alerts_enabled?(tenant)
    tenant.settings.dig('notifications', 'webhook_enabled') == true &&
      tenant.settings.dig('notifications', 'budget_webhook_url').present?
  end
end
