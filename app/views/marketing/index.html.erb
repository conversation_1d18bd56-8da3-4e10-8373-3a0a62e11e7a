<!-- Hero Section - Massive Value Proposition -->
<section class="relative overflow-hidden bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 min-h-screen flex items-center">
  <!-- Background Effects -->
  <div class="absolute inset-0">
    <div class="absolute top-0 left-0 w-96 h-96 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"></div>
    <div class="absolute top-0 right-0 w-96 h-96 bg-purple-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-1000"></div>
    <div class="absolute bottom-0 left-1/2 w-96 h-96 bg-pink-400 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <!-- Left Column - Value Proposition -->
      <div class="text-center lg:text-left">
        <!-- Social Proof Badge -->
        <div class="inline-flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2 mb-8 border border-gray-200 shadow-sm">
          <div class="flex -space-x-2">
            <img class="w-6 h-6 rounded-full border-2 border-white" src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face" alt="User">
            <img class="w-6 h-6 rounded-full border-2 border-white" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face" alt="User">
            <img class="w-6 h-6 rounded-full border-2 border-white" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face" alt="User">
          </div>
          <span class="text-sm font-medium text-gray-700">Join 10,000+ marketers</span>
          <div class="flex space-x-1">
            <% 5.times do %>
              <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            <% end %>
          </div>
        </div>
        
        <!-- Main Headline - Benefit Focused -->
        <h1 class="text-5xl lg:text-7xl font-black leading-tight mb-6">
          <span class="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent">
            Grow Your Business
          </span>
          <br>
          <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            10x Faster
          </span>
        </h1>
        
        <!-- Subheadline - Clear Value -->
        <p class="text-xl lg:text-2xl text-gray-700 mb-8 leading-relaxed">
          Stop wasting time on manual marketing. Our AI creates, optimizes, and manages your campaigns while you sleep. 
          <span class="font-semibold text-blue-600">Average users see 312% ROI increase</span> in just 30 days.
        </p>
        
        <!-- Benefits List -->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-10">
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700 font-medium">AI-powered campaign creation</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700 font-medium">Real-time optimization</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700 font-medium">Multi-platform automation</span>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <span class="text-gray-700 font-medium">Advanced analytics & insights</span>
          </div>
        </div>
        
        <!-- CTA Section - Urgency & Social Proof -->
        <div class="space-y-4">
          <div class="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
            <%= link_to new_user_registration_path, class: "group relative inline-flex items-center justify-center px-8 py-4 text-lg font-bold text-white bg-gradient-to-r from-blue-600 to-purple-600 rounded-full shadow-2xl hover:shadow-blue-500/25 transform hover:-translate-y-1 transition-all duration-300" do %>
              <span class="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-700 to-purple-700 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
              <span class="relative flex items-center space-x-2">
                <span>Start Free 14-Day Trial</span>
                <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              </span>
            <% end %>
            
            <button class="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-gray-700 bg-white border-2 border-gray-200 rounded-full hover:border-blue-300 hover:text-blue-600 transition-all duration-300">
              <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
              </svg>
              Watch Demo (2 min)
            </button>
          </div>
          
          <!-- Trust Indicators -->
          <div class="flex items-center justify-center lg:justify-start space-x-6 text-sm text-gray-600">
            <div class="flex items-center space-x-1">
              <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span>No credit card required</span>
            </div>
            <div class="flex items-center space-x-1">
              <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span>Cancel anytime</span>
            </div>
            <div class="flex items-center space-x-1">
              <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span>Setup in 5 minutes</span>
            </div>
          </div>
        </div>
        
        <!-- Urgency Notice -->
        <div class="mt-8 p-4 bg-orange-50 border border-orange-200 rounded-lg">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-orange-800 font-medium">🔥 Limited Time: Get 3 months free with annual plans - Expires in 48 hours!</span>
          </div>
        </div>
      </div>
      
      <!-- Right Column - Visual Elements -->
      <div class="relative">
        <!-- Dashboard Preview -->
        <div class="relative transform rotate-3 hover:rotate-0 transition-transform duration-500">
          <div class="bg-white rounded-2xl shadow-2xl border border-gray-200 overflow-hidden">
            <!-- Dashboard Header -->
            <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-white/20 rounded-lg"></div>
                <div class="flex-1">
                  <div class="h-3 bg-white/30 rounded w-32 mb-1"></div>
                  <div class="h-2 bg-white/20 rounded w-24"></div>
                </div>
              </div>
            </div>
            
            <!-- Dashboard Content -->
            <div class="p-6">
              <!-- Metrics Cards -->
              <div class="grid grid-cols-2 gap-4 mb-6">
                <div class="bg-gradient-to-br from-green-50 to-green-100 p-4 rounded-xl">
                  <div class="text-2xl font-bold text-green-600">312%</div>
                  <div class="text-sm text-green-700">ROI Increase</div>
                </div>
                <div class="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-xl">
                  <div class="text-2xl font-bold text-blue-600">+156K</div>
                  <div class="text-sm text-blue-700">New Leads</div>
                </div>
              </div>
              
              <!-- Chart Simulation -->
              <div class="space-y-2">
                <div class="h-2 bg-gradient-to-r from-blue-200 to-blue-400 rounded-full w-full"></div>
                <div class="h-2 bg-gradient-to-r from-purple-200 to-purple-400 rounded-full w-4/5"></div>
                <div class="h-2 bg-gradient-to-r from-green-200 to-green-400 rounded-full w-3/4"></div>
                <div class="h-2 bg-gradient-to-r from-orange-200 to-orange-400 rounded-full w-2/3"></div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Floating Elements -->
        <div class="absolute -top-6 -right-6 w-24 h-24 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-white font-bold text-lg animate-bounce">
          AI
        </div>
        
        <div class="absolute -bottom-6 -left-6 w-20 h-20 bg-gradient-to-br from-pink-400 to-red-500 rounded-full flex items-center justify-center text-white animate-pulse">
          <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clip-rule="evenodd"></path>
          </svg>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Social Proof Section -->
<section class="py-16 bg-white">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-12">
      <h2 class="text-lg font-semibold text-gray-600 mb-4">Trusted by industry leaders</h2>
      <div class="grid grid-cols-2 md:grid-cols-5 gap-8 items-center opacity-70">
        <!-- Logo placeholders with proper branding colors -->
        <div class="flex items-center justify-center">
          <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-6 py-3 rounded-lg font-bold">TechCorp</div>
        </div>
        <div class="flex items-center justify-center">
          <div class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg font-bold">GrowthCo</div>
        </div>
        <div class="flex items-center justify-center">
          <div class="bg-gradient-to-r from-purple-600 to-purple-700 text-white px-6 py-3 rounded-lg font-bold">ScaleUp</div>
        </div>
        <div class="flex items-center justify-center">
          <div class="bg-gradient-to-r from-orange-600 to-orange-700 text-white px-6 py-3 rounded-lg font-bold">InnovateLab</div>
        </div>
        <div class="flex items-center justify-center">
          <div class="bg-gradient-to-r from-red-600 to-red-700 text-white px-6 py-3 rounded-lg font-bold">FutureTech</div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Problem/Solution Section -->
<section class="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
      <!-- Problem Side -->
      <div>
        <div class="text-red-600 font-semibold mb-4">❌ The Problem</div>
        <h2 class="text-4xl font-bold text-gray-900 mb-6">Marketing is broken for small businesses</h2>
        <div class="space-y-4 text-gray-700">
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Too many tools, too much complexity</h3>
              <p class="text-gray-600">Managing 15+ different marketing tools costs $2,000+/month and requires a full team</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Manual work eats up your time</h3>
              <p class="text-gray-600">Spending 30+ hours/week on repetitive tasks instead of growing your business</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Poor results with limited insights</h3>
              <p class="text-gray-600">Can't optimize campaigns effectively without AI-powered analytics and recommendations</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Solution Side -->
      <div>
        <div class="text-green-600 font-semibold mb-4">✅ The Solution</div>
        <h2 class="text-4xl font-bold text-gray-900 mb-6">One AI platform that does it all</h2>
        <div class="space-y-4 text-gray-700">
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">All-in-one marketing automation</h3>
              <p class="text-gray-600">Email, social media, ads, analytics - everything in one platform for just $49/month</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">AI does the heavy lifting</h3>
              <p class="text-gray-600">Set it once, let AI create, optimize, and manage campaigns 24/7 while you focus on growth</p>
            </div>
          </div>
          <div class="flex items-start space-x-3">
            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">Predictable, measurable growth</h3>
              <p class="text-gray-600">Advanced AI analytics show exactly what's working and automatically improve performance</p>
            </div>
          </div>
        </div>
        
        <!-- Results Showcase -->
        <div class="mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border border-green-200">
          <h4 class="font-semibold text-gray-900 mb-4">Average results in first 30 days:</h4>
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-green-600">312%</div>
              <div class="text-sm text-gray-600">ROI Increase</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-blue-600">2.5x</div>
              <div class="text-sm text-gray-600">More Leads</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-purple-600">87%</div>
              <div class="text-sm text-gray-600">Time Saved</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<%= render 'sections' %>