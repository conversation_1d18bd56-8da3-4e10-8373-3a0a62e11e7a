<% content_for :title, "Dashboard" %>

<!-- Modern Dashboard Container with Stimulus Controller -->
<div class="space-y-8" data-controller="dashboard" data-dashboard-refresh-interval-value="30000">
  <!-- Header Section with Time-based Greeting -->
  <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
    <div class="flex-1">
      <div class="flex items-center space-x-3 mb-2">
        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center float-icon">
          <span class="text-white text-lg font-semibold">
            <%= current_user.email.first.upcase %>
          </span>
        </div>
        <div>
          <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">
            Good <%= Time.current.hour < 12 ? 'morning' : Time.current.hour < 18 ? 'afternoon' : 'evening' %>, <%= current_user.email.split('@').first.capitalize %>!
          </h1>
          <p class="text-gray-600">Here's what's happening with your marketing campaigns today.</p>
        </div>
      </div>
    </div>
    <div class="flex items-center space-x-3 mt-4 lg:mt-0">
      <div class="text-right">
        <p class="text-sm text-gray-500">Last updated</p>
        <p class="text-sm font-medium text-gray-900"><%= Time.current.strftime("%B %d, %Y at %I:%M %p") %></p>
      </div>
      <%= link_to new_campaign_path, 
          class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 animated-gradient",
          data: { action: "click->dashboard#createCampaign" } do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        New Campaign
      <% end %>
    </div>
  </div>

  <!-- Enhanced Stats Cards with Trends -->
  <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
    <!-- Total Campaigns Card -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 card-hover dashboard-card" 
         data-dashboard-target="card"
         data-action="mouseenter->dashboard#cardHover mouseleave->dashboard#cardLeave">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg float-icon">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          +12%
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Total Campaigns</p>
        <p class="text-3xl font-bold text-gray-900" data-dashboard-target="metric"><%= @campaign_stats[:total] %></p>
        <p class="text-xs text-gray-500 mt-2">
          <span class="text-green-600 font-medium">+3 this month</span>
        </p>
      </div>
    </div>

    <!-- Active Campaigns Card -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 card-hover dashboard-card" 
         data-dashboard-target="card"
         data-action="mouseenter->dashboard#cardHover mouseleave->dashboard#cardLeave">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg float-icon">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          +8%
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Active Campaigns</p>
        <p class="text-3xl font-bold text-emerald-600" data-dashboard-target="metric"><%= @campaign_stats[:active] %></p>
        <p class="text-xs text-gray-500 mt-2">
          <span class="text-emerald-600 font-medium">Running strong</span>
        </p>
      </div>
    </div>

    <!-- Total Budget Card -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 card-hover dashboard-card" 
         data-dashboard-target="card"
         data-action="mouseenter->dashboard#cardHover mouseleave->dashboard#cardLeave">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-violet-600 rounded-xl flex items-center justify-center shadow-lg float-icon">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          +15%
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Total Budget</p>
        <p class="text-3xl font-bold text-gray-900" data-dashboard-target="metric">$<%= number_with_precision(@budget_stats[:total_budget], precision: 0, delimiter: ',') %></p>
        <p class="text-xs text-gray-500 mt-2">
          <span class="text-blue-600 font-medium">Allocated funds</span>
        </p>
      </div>
    </div>

    <!-- Performance Score Card -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 card-hover dashboard-card" 
         data-dashboard-target="card"
         data-action="mouseenter->dashboard#cardHover mouseleave->dashboard#cardLeave">
      <div class="flex items-center justify-between mb-4">
        <div class="w-12 h-12 bg-gradient-to-br from-amber-500 to-amber-600 rounded-xl flex items-center justify-center shadow-lg float-icon">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 pulse-indicator">
          Live
        </span>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-600 mb-1">Performance Score</p>
        <p class="text-3xl font-bold text-amber-600" data-dashboard-target="metric">
          <%= ((@campaign_stats[:active].to_f / [@campaign_stats[:total], 1].max) * 100).round %>%
        </p>
        <p class="text-xs text-gray-500 mt-2">
          <span class="text-amber-600 font-medium">Above average</span>
        </p>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    
    <!-- Left Column - Recent Activity & Campaigns -->
    <div class="lg:col-span-2 space-y-8">
      
      <!-- Budget Overview Chart -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 dashboard-card" data-dashboard-target="card">
        <div class="flex items-center justify-between mb-6">
          <h2 class="text-xl font-semibold text-gray-900">Budget Overview</h2>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">This month</span>
            <div class="w-3 h-3 bg-green-500 rounded-full pulse-indicator"></div>
          </div>
        </div>
        
        <!-- Budget Progress Bars -->
        <div class="space-y-6">
          <div>
            <div class="flex justify-between items-center mb-3">
              <span class="text-sm font-medium text-gray-700">Total Budget</span>
              <span class="text-lg font-bold text-gray-900">$<%= number_with_precision(@budget_stats[:total_budget], precision: 0, delimiter: ',') %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3 progress-bar">
              <div class="bg-gradient-to-r from-blue-500 to-violet-600 h-3 rounded-full transition-all duration-1000" 
                   data-dashboard-target="progressBar" 
                   data-width="100"></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between items-center mb-3">
              <span class="text-sm font-medium text-gray-700">Active Budget</span>
              <span class="text-lg font-bold text-emerald-600">$<%= number_with_precision(@budget_stats[:active_budget], precision: 0, delimiter: ',') %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3 progress-bar">
              <div class="bg-gradient-to-r from-emerald-500 to-teal-600 h-3 rounded-full transition-all duration-1000" 
                   data-dashboard-target="progressBar" 
                   data-width="<%= (@budget_stats[:active_budget] / [@budget_stats[:total_budget], 1].max * 100).round %>"></div>
            </div>
          </div>
          
          <div>
            <div class="flex justify-between items-center mb-3">
              <span class="text-sm font-medium text-gray-700">Spent Budget</span>
              <span class="text-lg font-bold text-amber-600">$<%= number_with_precision(@budget_stats[:spent_budget], precision: 0, delimiter: ',') %></span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3 progress-bar">
              <div class="bg-gradient-to-r from-amber-500 to-orange-500 h-3 rounded-full transition-all duration-1000" 
                   data-dashboard-target="progressBar" 
                   data-width="<%= (@budget_stats[:spent_budget] / [@budget_stats[:total_budget], 1].max * 100).round %>"></div>
            </div>
          </div>
        </div>
        
        <!-- Budget Insights -->
        <div class="mt-6 pt-6 border-t border-gray-100">
          <div class="grid grid-cols-3 gap-4">
            <div class="text-center group hover:bg-blue-50 rounded-lg p-3 transition-colors">
              <p class="text-2xl font-bold text-blue-600" data-dashboard-target="metric">
                <%= ((@budget_stats[:active_budget] / [@budget_stats[:total_budget], 1].max) * 100).round %>%
              </p>
              <p class="text-xs text-gray-500">Active</p>
            </div>
            <div class="text-center group hover:bg-amber-50 rounded-lg p-3 transition-colors">
              <p class="text-2xl font-bold text-amber-600" data-dashboard-target="metric">
                <%= ((@budget_stats[:spent_budget] / [@budget_stats[:total_budget], 1].max) * 100).round %>%
              </p>
              <p class="text-xs text-gray-500">Spent</p>
            </div>
            <div class="text-center group hover:bg-emerald-50 rounded-lg p-3 transition-colors">
              <p class="text-2xl font-bold text-emerald-600" data-dashboard-target="metric">
                <%= (((@budget_stats[:total_budget] - @budget_stats[:spent_budget]) / [@budget_stats[:total_budget], 1].max) * 100).round %>%
              </p>
              <p class="text-xs text-gray-500">Remaining</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Recent Campaigns with Enhanced Design -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900">Recent Campaigns</h2>
            <%= link_to campaigns_path, class: "inline-flex items-center px-3 py-1.5 bg-blue-50 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors text-sm font-medium" do %>
              View All
              <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            <% end %>
          </div>
        </div>
        
        <div class="p-6">
          <% if @recent_campaigns.any? %>
            <div class="space-y-4">
              <% @recent_campaigns.each do |campaign| %>
                    <div class="group relative bg-gray-50 hover:bg-gray-100 rounded-xl p-4 transition-all duration-200 hover:shadow-md">
                      <div class="flex items-start justify-between">
                        <div class="flex items-start space-x-4 flex-1 min-w-0">
                          <div class="flex-shrink-0">
                            <div class="w-12 h-12 rounded-xl flex items-center justify-center
                              <%= case campaign.status
                                  when 'active' then 'bg-gradient-to-br from-emerald-500 to-emerald-600 text-white'
                                  when 'draft' then 'bg-gradient-to-br from-slate-400 to-slate-500 text-white'
                                  when 'completed' then 'bg-gradient-to-br from-indigo-500 to-indigo-600 text-white'
                                  else 'bg-gradient-to-br from-neutral-400 to-neutral-500 text-white'
                                  end %>">
                              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                              </svg>
                            </div>
                          </div>
                          
                          <div class="flex-1 min-w-0">
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                              <%= campaign.name %>
                            </h3>
                            <div class="flex items-center space-x-3 mt-1">
                              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                <%= case campaign.status
                                    when 'active' then 'bg-emerald-100 text-emerald-800'
                                    when 'draft' then 'bg-gray-100 text-gray-800'
                                    when 'completed' then 'bg-indigo-100 text-indigo-800'
                                    else 'bg-gray-100 text-gray-800'
                                    end %>">
                                <%= campaign.status.titleize %>
                              </span>
                              <span class="text-sm text-gray-500">
                                $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %>
                              </span>
                            </div>
                            <div class="flex items-center space-x-2 mt-2">
                              <% if campaign.email_campaign %>
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800">
                                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                  </svg>
                                  Email
                                </span>
                              <% end %>
                              <% if campaign.social_campaign %>
                                <span class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-pink-100 text-pink-800">
                                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-5 8l-4-4m0 0l4-4m-4 4h12"></path>
                                  </svg>
                                  Social
                                </span>
                              <% end %>
                            </div>
                          </div>
                        </div>
                    
                    <div class="flex-shrink-0 ml-4">
                      <%= link_to campaign_path(campaign), class: "inline-flex items-center px-3 py-1.5 bg-white border border-gray-200 text-gray-700 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors text-sm" do %>
                        View
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                      <% end %>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <!-- Empty State -->
            <div class="text-center py-12">
              <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
              <h3 class="text-lg font-semibold text-gray-900 mb-2">No campaigns yet</h3>
              <p class="text-gray-500 mb-6">Create your first marketing campaign to get started.</p>
              <%= link_to new_campaign_path, class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Create Campaign
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Right Sidebar - Platform Stats & Quick Actions -->
    <div class="space-y-8">
      
      <!-- Platform Distribution -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900">Platform Distribution</h2>
            <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
          </div>
        </div>
        
        <div class="p-6">
          <div class="space-y-6">
            <!-- Email Campaigns -->
            <div class="group hover:bg-blue-50 rounded-xl p-4 transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900">Email Campaigns</h3>
                    <p class="text-sm text-gray-500">Direct marketing reach</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-2xl font-bold text-blue-600"><%= @platform_stats[:email_campaigns] %></p>
                  <p class="text-xs text-gray-500">campaigns</p>
                </div>
              </div>
            </div>
            
            <!-- Social Campaigns -->
            <div class="group hover:bg-pink-50 rounded-xl p-4 transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-5 8l-4-4m0 0l4-4m-4 4h12"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900">Social Campaigns</h3>
                    <p class="text-sm text-gray-500">Social media presence</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-2xl font-bold text-pink-600"><%= @platform_stats[:social_campaigns] %></p>
                  <p class="text-xs text-gray-500">campaigns</p>
                </div>
              </div>
            </div>
            
            <!-- Multi-Channel -->
            <div class="group hover:bg-purple-50 rounded-xl p-4 transition-colors">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900">Multi-Channel</h3>
                    <p class="text-sm text-gray-500">Integrated approach</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-2xl font-bold text-purple-600"><%= @platform_stats[:multi_channel] %></p>
                  <p class="text-xs text-gray-500">campaigns</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-2xl p-6 text-white">
        <div class="text-center mb-6">
          <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-semibold mb-2">Ready to Scale?</h3>
          <p class="text-blue-100 text-sm">Launch your next multi-channel campaign with AI-powered insights.</p>
        </div>
        
        <div class="space-y-3">
          <%= link_to new_campaign_path, class: "w-full bg-white text-blue-600 px-4 py-3 rounded-xl font-semibold hover:bg-gray-50 transition-all duration-200 text-center block shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <div class="flex items-center justify-center">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Create New Campaign
            </div>
          <% end %>
          
          <%= link_to campaigns_path, class: "w-full bg-white/10 text-white px-4 py-2 rounded-xl font-medium hover:bg-white/20 transition-colors text-center block border border-white/20" do %>
            View All Campaigns
          <% end %>
        </div>
      </div>

      <!-- Performance Insights -->
      <div class="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Performance Insights</h3>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-900">Campaign Success Rate</span>
            </div>
            <span class="text-lg font-bold text-green-600">
              <%= ((@campaign_stats[:active].to_f + @campaign_stats[:completed].to_f) / [@campaign_stats[:total], 1].max * 100).round %>%
            </span>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-900">Average ROI</span>
            </div>
            <span class="text-lg font-bold text-blue-600">+245%</span>
          </div>
          
          <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <span class="text-sm font-medium text-gray-900">Engagement Rate</span>
            </div>
            <span class="text-lg font-bold text-purple-600">8.2%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
