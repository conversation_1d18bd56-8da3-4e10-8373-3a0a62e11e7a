<!-- Campaigns Index Page -->

<!-- <PERSON> Header -->
<div class="mb-8">
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Campaigns</h1>
      <p class="text-gray-600">Manage and monitor all your marketing campaigns in one place.</p>
    </div>
    <div class="mt-4 sm:mt-0">
      <%= link_to new_campaign_path,
          class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        New Campaign
      <% end %>
    </div>
  </div>
</div>

<!-- Stats Cards -->
<div class="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
  <!-- Total Campaigns -->
  <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
    <div class="text-center">
      <p class="text-2xl font-bold text-gray-900"><%= @campaign_stats[:total] %></p>
      <p class="text-sm text-gray-600">Total</p>
    </div>
  </div>

  <!-- Active Campaigns -->
  <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
    <div class="text-center">
      <p class="text-2xl font-bold text-green-600"><%= @campaign_stats[:active] %></p>
      <p class="text-sm text-gray-600">Active</p>
    </div>
  </div>

  <!-- Draft Campaigns -->
  <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
    <div class="text-center">
      <p class="text-2xl font-bold text-gray-600"><%= @campaign_stats[:draft] %></p>
      <p class="text-sm text-gray-600">Draft</p>
    </div>
  </div>

  <!-- Paused Campaigns -->
  <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
    <div class="text-center">
      <p class="text-2xl font-bold text-orange-600"><%= @campaign_stats[:paused] %></p>
      <p class="text-sm text-gray-600">Paused</p>
    </div>
  </div>

  <!-- Completed Campaigns -->
  <div class="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
    <div class="text-center">
      <p class="text-2xl font-bold text-blue-600"><%= @campaign_stats[:completed] %></p>
      <p class="text-sm text-gray-600">Completed</p>
    </div>
  </div>
</div>

<!-- Filters and Search -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 mb-8">
  <%= form_with url: campaigns_path, method: :get, local: true, class: "space-y-4 md:space-y-0 md:flex md:items-center md:space-x-4" do |form| %>
    <!-- Search -->
    <div class="flex-1">
      <%= form.text_field :search,
          placeholder: "Search campaigns...",
          value: params[:search],
          class: "w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" %>
    </div>

    <!-- Status Filter -->
    <div>
      <%= form.select :status,
          options_for_select([
            ['All Statuses', ''],
            ['Active', 'active'],
            ['Draft', 'draft'],
            ['Paused', 'paused'],
            ['Completed', 'completed'],
            ['Cancelled', 'cancelled']
          ], params[:status]),
          {},
          { class: "px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
    </div>

    <!-- Type Filter -->
    <div>
      <%= form.select :type,
          options_for_select([
            ['All Types', ''],
            ['Email', 'email'],
            ['Social', 'social'],
            ['SEO', 'seo'],
            ['Multi-Channel', 'multi_channel']
          ], params[:type]),
          {},
          { class: "px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
    </div>

    <!-- Sort -->
    <div>
      <%= form.select :sort,
          options_for_select([
            ['Recent', ''],
            ['Name', 'name'],
            ['Budget', 'budget'],
            ['Start Date', 'start_date'],
            ['Status', 'status']
          ], params[:sort]),
          {},
          { class: "px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" } %>
    </div>

    <!-- Submit Button -->
    <div class="flex space-x-2">
      <%= form.submit "Filter",
          class: "px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" %>
      <%= link_to "Clear", campaigns_path,
          class: "px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors" %>
    </div>
  <% end %>
</div>

<!-- Campaigns List -->
<% if @campaigns.any? %>
  <!-- Desktop Table View -->
  <div class="hidden lg:block bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Campaign</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dates</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progress</th>
            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <% @campaigns.each do |campaign| %>
            <tr class="hover:bg-gray-50 transition-colors">
              <!-- Campaign Info -->
              <td class="px-6 py-4">
                <div>
                  <div class="text-sm font-medium text-gray-900">
                    <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-blue-600" %>
                  </div>
                  <div class="text-sm text-gray-500 truncate max-w-xs">
                    <%= campaign.description %>
                  </div>
                  <div class="text-xs text-gray-400 mt-1">
                    Target: <%= campaign.target_audience %>
                  </div>
                </div>
              </td>

              <!-- Type -->
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <% case campaign.campaign_type %>
                  <% when 'email' %>
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <span class="text-blue-600 font-bold text-sm">@</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">Email</span>
                  <% when 'social' %>
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                      <span class="text-green-600 font-bold text-sm">#</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">Social</span>
                  <% when 'seo' %>
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                      <span class="text-purple-600 font-bold text-sm">S</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">SEO</span>
                  <% when 'multi_channel' %>
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <span class="text-orange-600 font-bold text-sm">⚡</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">Multi-Channel</span>
                  <% end %>
                </div>
              </td>

              <!-- Status -->
              <td class="px-6 py-4">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                  <%= case campaign.status
                      when 'active' then 'bg-green-100 text-green-800'
                      when 'draft' then 'bg-gray-100 text-gray-800'
                      when 'paused' then 'bg-yellow-100 text-yellow-800'
                      when 'completed' then 'bg-blue-100 text-blue-800'
                      when 'cancelled' then 'bg-red-100 text-red-800'
                      else 'bg-gray-100 text-gray-800'
                      end %>">
                  <%= campaign.status.titleize %>
                </span>
              </td>

              <!-- Budget -->
              <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900">
                  $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %>
                </div>
              </td>

              <!-- Dates -->
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900">
                  <% if campaign.start_date %>
                    <div><%= campaign.start_date.strftime("%b %d, %Y") %></div>
                  <% else %>
                    <div class="text-gray-400">Not set</div>
                  <% end %>
                  <% if campaign.end_date %>
                    <div class="text-xs text-gray-500">to <%= campaign.end_date.strftime("%b %d, %Y") %></div>
                  <% end %>
                </div>
              </td>

              <!-- Progress -->
              <td class="px-6 py-4">
                <div class="flex items-center">
                  <div class="flex-1">
                    <div class="flex items-center justify-between text-sm mb-1">
                      <span class="text-gray-600">Progress</span>
                      <span class="font-medium"><%= campaign.progress_percentage %>%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                           style="width: <%= campaign.progress_percentage %>%"></div>
                    </div>
                  </div>
                </div>
              </td>

              <!-- Actions -->
              <td class="px-6 py-4 text-right">
                <div class="flex items-center justify-end space-x-2">
                  <%= link_to campaign_path(campaign),
                      class: "text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors",
                      title: "View Campaign" do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  <% end %>

                  <%= link_to edit_campaign_path(campaign),
                      class: "text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors",
                      title: "Edit Campaign" do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  <% end %>

                  <!-- Status Action Button -->
                  <% if campaign.can_be_activated? %>
                    <%= link_to activate_campaign_path(campaign), method: :patch,
                        class: "text-green-600 hover:text-green-700 p-2 rounded-lg hover:bg-green-50 transition-colors",
                        title: "Activate Campaign",
                        data: { confirm: "Are you sure you want to activate this campaign?" } do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4V8a3 3 0 016 0v2M7 16a3 3 0 006 0v-2"></path>
                      </svg>
                    <% end %>
                  <% elsif campaign.active? %>
                    <%= link_to pause_campaign_path(campaign), method: :patch,
                        class: "text-yellow-600 hover:text-yellow-700 p-2 rounded-lg hover:bg-yellow-50 transition-colors",
                        title: "Pause Campaign",
                        data: { confirm: "Are you sure you want to pause this campaign?" } do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 9v6m4-6v6m7-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    <% end %>
                  <% end %>

                  <%= link_to campaign_path(campaign), method: :delete,
                      class: "text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors",
                      title: "Delete Campaign",
                      data: { confirm: "Are you sure you want to delete this campaign? This action cannot be undone." } do %>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  <% end %>
                </div>
              </td>
            </tr>
          <% end %>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Mobile Card View -->
  <div class="lg:hidden space-y-4">
    <% @campaigns.each do |campaign| %>
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <!-- Campaign Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-gray-900 mb-1">
              <%= link_to campaign.name, campaign_path(campaign), class: "hover:text-blue-600" %>
            </h3>
            <p class="text-sm text-gray-600 mb-2"><%= campaign.description %></p>
            <p class="text-xs text-gray-500">Target: <%= campaign.target_audience %></p>
          </div>

          <!-- Status Badge -->
          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-4
            <%= case campaign.status
                when 'active' then 'bg-green-100 text-green-800'
                when 'draft' then 'bg-gray-100 text-gray-800'
                when 'paused' then 'bg-yellow-100 text-yellow-800'
                when 'completed' then 'bg-blue-100 text-blue-800'
                when 'cancelled' then 'bg-red-100 text-red-800'
                else 'bg-gray-100 text-gray-800'
                end %>">
            <%= campaign.status.titleize %>
          </span>
        </div>

        <!-- Campaign Details -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <!-- Type -->
          <div>
            <p class="text-xs text-gray-500 mb-1">Type</p>
            <div class="flex items-center">
              <% case campaign.campaign_type %>
              <% when 'email' %>
                <div class="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-blue-600 font-bold text-xs">@</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Email</span>
              <% when 'social' %>
                <div class="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-green-600 font-bold text-xs">#</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Social</span>
              <% when 'seo' %>
                <div class="w-6 h-6 bg-purple-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-purple-600 font-bold text-xs">S</span>
                </div>
                <span class="text-sm font-medium text-gray-900">SEO</span>
              <% when 'multi_channel' %>
                <div class="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center mr-2">
                  <span class="text-orange-600 font-bold text-xs">⚡</span>
                </div>
                <span class="text-sm font-medium text-gray-900">Multi-Channel</span>
              <% end %>
            </div>
          </div>

          <!-- Budget -->
          <div>
            <p class="text-xs text-gray-500 mb-1">Budget</p>
            <p class="text-sm font-medium text-gray-900">
              $<%= number_with_precision(campaign.budget_in_dollars, precision: 0, delimiter: ',') %>
            </p>
          </div>
        </div>

        <!-- Dates -->
        <div class="mb-4">
          <p class="text-xs text-gray-500 mb-1">Duration</p>
          <div class="text-sm text-gray-900">
            <% if campaign.start_date %>
              <%= campaign.start_date.strftime("%b %d, %Y") %>
              <% if campaign.end_date %>
                - <%= campaign.end_date.strftime("%b %d, %Y") %>
              <% end %>
            <% else %>
              <span class="text-gray-400">Dates not set</span>
            <% end %>
          </div>
        </div>

        <!-- Progress -->
        <div class="mb-4">
          <div class="flex items-center justify-between text-sm mb-2">
            <span class="text-gray-600">Progress</span>
            <span class="font-medium"><%= campaign.progress_percentage %>%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                 style="width: <%= campaign.progress_percentage %>%"></div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
          <div class="flex items-center space-x-3">
            <%= link_to campaign_path(campaign),
                class: "text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors",
                title: "View Campaign" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
            <% end %>

            <%= link_to edit_campaign_path(campaign),
                class: "text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50 transition-colors",
                title: "Edit Campaign" do %>
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            <% end %>
          </div>

          <div class="flex items-center space-x-2">
            <!-- Status Action Button -->
            <% if campaign.can_be_activated? %>
              <%= link_to activate_campaign_path(campaign), method: :patch,
                  class: "px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full hover:bg-green-200 transition-colors",
                  data: { confirm: "Are you sure you want to activate this campaign?" } do %>
                Activate
              <% end %>
            <% elsif campaign.active? %>
              <%= link_to pause_campaign_path(campaign), method: :patch,
                  class: "px-3 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 rounded-full hover:bg-yellow-200 transition-colors",
                  data: { confirm: "Are you sure you want to pause this campaign?" } do %>
                Pause
              <% end %>
            <% end %>

            <%= link_to campaign_path(campaign), method: :delete,
                class: "text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors",
                title: "Delete Campaign",
                data: { confirm: "Are you sure you want to delete this campaign? This action cannot be undone." } do %>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>

<% else %>
  <!-- Empty State -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
    <div class="max-w-md mx-auto">
      <!-- Empty State Icon -->
      <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
        </svg>
      </div>

      <!-- Empty State Content -->
      <h3 class="text-xl font-semibold text-gray-900 mb-2">No campaigns found</h3>
      <p class="text-gray-600 mb-8">
        <% if params[:search].present? || params[:status].present? || params[:type].present? %>
          No campaigns match your current filters. Try adjusting your search criteria.
        <% else %>
          Get started by creating your first AI-powered marketing campaign.
        <% end %>
      </p>

      <!-- Empty State Actions -->
      <div class="flex flex-col sm:flex-row gap-4 justify-center">
        <%= link_to new_campaign_path,
            class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Create Your First Campaign
        <% end %>

        <% if params[:search].present? || params[:status].present? || params[:type].present? %>
          <%= link_to campaigns_path,
              class: "inline-flex items-center px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
            Clear Filters
          <% end %>
        <% end %>
      </div>
    </div>
  </div>
<% end %>

<!-- Pagination -->
<% if defined?(@pagy) && @pagy.pages > 1 %>
  <div class="mt-8 flex justify-center">
    <nav class="flex items-center space-x-2">
      <!-- Previous Page -->
      <% if @pagy.prev %>
        <%= link_to campaigns_path(page: @pagy.prev, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort]),
            class: "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50" do %>
          Previous
        <% end %>
      <% else %>
        <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
          Previous
        </span>
      <% end %>

      <!-- Page Numbers -->
      <% @pagy.series.each do |item| %>
        <% if item.is_a?(Integer) %>
          <% if item == @pagy.page %>
            <span class="px-3 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md">
              <%= item %>
            </span>
          <% else %>
            <%= link_to campaigns_path(page: item, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort]),
                class: "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50" do %>
              <%= item %>
            <% end %>
          <% end %>
        <% elsif item == :gap %>
          <span class="px-3 py-2 text-sm font-medium text-gray-300">...</span>
        <% end %>
      <% end %>

      <!-- Next Page -->
      <% if @pagy.next %>
        <%= link_to campaigns_path(page: @pagy.next, search: params[:search], status: params[:status], type: params[:type], sort: params[:sort]),
            class: "px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50" do %>
          Next
        <% end %>
      <% else %>
        <span class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-100 border border-gray-300 rounded-md cursor-not-allowed">
          Next
        </span>
      <% end %>
    </nav>
  </div>
<% end %>
