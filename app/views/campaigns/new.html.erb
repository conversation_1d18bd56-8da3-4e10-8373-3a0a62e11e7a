<!-- New Campaign Page -->

<!-- <PERSON> Header -->
<div class="mb-8">
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Create New Campaign</h1>
      <p class="text-gray-600">Set up your AI-powered marketing campaign with intelligent automation.</p>
    </div>
    <div>
      <%= link_to campaigns_path,
          class: "inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Campaigns
      <% end %>
    </div>
  </div>
</div>

<!-- Campaign Form -->
<div class="bg-white rounded-xl shadow-sm border border-gray-100">
  <%= form_with model: @campaign, local: true, class: "space-y-8" do |form| %>

    <!-- Form Header -->
    <div class="px-8 py-6 border-b border-gray-100">
      <h2 class="text-xl font-semibold text-gray-900">Campaign Details</h2>
      <p class="text-gray-600 mt-1">Configure the basic settings for your marketing campaign.</p>
    </div>

    <!-- Error Messages -->
    <% if @campaign.errors.any? %>
      <div class="mx-8 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <svg class="w-5 h-5 text-red-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">
              <%= pluralize(@campaign.errors.count, "error") %> prohibited this campaign from being saved:
            </h3>
            <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
              <% @campaign.errors.full_messages.each do |message| %>
                <li><%= message %></li>
              <% end %>
            </ul>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Form Content -->
    <div class="px-8 space-y-8">

      <!-- Basic Information -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Campaign Name -->
        <div class="lg:col-span-2">
          <%= form.label :name, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.text_field :name,
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
              placeholder: "Enter campaign name (e.g., Holiday Sale 2024)" %>
        </div>

        <!-- Campaign Type -->
        <div>
          <%= form.label :campaign_type, "Campaign Type", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :campaign_type,
              options_for_select([
                ['Email Marketing', 'email'],
                ['Social Media', 'social'],
                ['SEO Optimization', 'seo'],
                ['Multi-Channel', 'multi_channel']
              ], @campaign.campaign_type),
              { prompt: 'Select campaign type' },
              { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" } %>
        </div>

        <!-- Status -->
        <div>
          <%= form.label :status, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.select :status,
              options_for_select([
                ['Draft', 'draft'],
                ['Active', 'active'],
                ['Paused', 'paused']
              ], @campaign.status || 'draft'),
              {},
              { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" } %>
        </div>
      </div>

      <!-- Description -->
      <div>
        <%= form.label :description, class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_area :description, rows: 4,
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors resize-none",
            placeholder: "Describe your campaign goals, target audience, and key messaging..." %>
      </div>

      <!-- Target Audience -->
      <div>
        <%= form.label :target_audience, class: "block text-sm font-medium text-gray-700 mb-2" %>
        <%= form.text_field :target_audience,
            class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
            placeholder: "e.g., Existing customers aged 25-45 interested in technology" %>
      </div>

      <!-- Budget and Dates -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Budget -->
        <div>
          <%= form.label :budget_in_dollars, "Budget ($)", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span class="text-gray-500 sm:text-sm">$</span>
            </div>
            <%= form.number_field :budget_in_dollars, step: 0.01, min: 0,
                class: "w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
                placeholder: "0.00" %>
          </div>
        </div>

        <!-- Start Date -->
        <div>
          <%= form.label :start_date, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.date_field :start_date,
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" %>
        </div>

        <!-- End Date -->
        <div>
          <%= form.label :end_date, class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.date_field :end_date,
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors" %>
        </div>
      </div>

      <!-- AI Campaign Goals (Optional) -->
      <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
        <h3 class="text-lg font-semibold text-blue-900 mb-4 flex items-center">
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
          </svg>
          AI-Powered Campaign Goals
        </h3>
        <p class="text-blue-700 mb-4">Let our AI help optimize your campaign for specific business objectives.</p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <label class="flex items-center p-3 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors">
            <input type="checkbox" name="campaign[settings][goals][]" value="increase_sales" class="text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <span class="ml-3 text-sm font-medium text-gray-700">Increase Sales</span>
          </label>

          <label class="flex items-center p-3 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors">
            <input type="checkbox" name="campaign[settings][goals][]" value="brand_awareness" class="text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <span class="ml-3 text-sm font-medium text-gray-700">Brand Awareness</span>
          </label>

          <label class="flex items-center p-3 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors">
            <input type="checkbox" name="campaign[settings][goals][]" value="lead_generation" class="text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <span class="ml-3 text-sm font-medium text-gray-700">Lead Generation</span>
          </label>

          <label class="flex items-center p-3 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors">
            <input type="checkbox" name="campaign[settings][goals][]" value="customer_retention" class="text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <span class="ml-3 text-sm font-medium text-gray-700">Customer Retention</span>
          </label>

          <label class="flex items-center p-3 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors">
            <input type="checkbox" name="campaign[settings][goals][]" value="website_traffic" class="text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <span class="ml-3 text-sm font-medium text-gray-700">Website Traffic</span>
          </label>

          <label class="flex items-center p-3 bg-white rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-50 transition-colors">
            <input type="checkbox" name="campaign[settings][goals][]" value="engagement" class="text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <span class="ml-3 text-sm font-medium text-gray-700">Engagement</span>
          </label>
        </div>
      </div>
    </div>

    <!-- Form Footer -->
    <div class="px-8 py-6 bg-gray-50 border-t border-gray-100 rounded-b-xl">
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          <p>💡 <strong>Pro Tip:</strong> Our AI will analyze your settings and suggest optimizations after creation.</p>
        </div>

        <div class="flex items-center space-x-4">
          <%= link_to campaigns_path,
              class: "px-6 py-3 bg-gray-200 text-gray-700 font-medium rounded-lg hover:bg-gray-300 transition-colors" do %>
            Cancel
          <% end %>

          <%= form.submit "Create Campaign",
              class: "px-8 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-200" %>
        </div>
      </div>
    </div>
  <% end %>
</div>

<!-- Campaign Type Information -->
<div class="mt-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
  <!-- Email Campaign Info -->
  <div class="bg-blue-50 rounded-lg p-6 border border-blue-200">
    <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mb-4">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
      </svg>
    </div>
    <h3 class="text-lg font-semibold text-blue-900 mb-2">Email Marketing</h3>
    <p class="text-blue-700 text-sm">Automated email sequences, personalization, and A/B testing powered by AI.</p>
  </div>

  <!-- Social Media Info -->
  <div class="bg-green-50 rounded-lg p-6 border border-green-200">
    <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mb-4">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"></path>
      </svg>
    </div>
    <h3 class="text-lg font-semibold text-green-900 mb-2">Social Media</h3>
    <p class="text-green-700 text-sm">Cross-platform posting, content optimization, and engagement tracking.</p>
  </div>

  <!-- SEO Info -->
  <div class="bg-purple-50 rounded-lg p-6 border border-purple-200">
    <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mb-4">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
      </svg>
    </div>
    <h3 class="text-lg font-semibold text-purple-900 mb-2">SEO Optimization</h3>
    <p class="text-purple-700 text-sm">Keyword research, content optimization, and ranking improvements.</p>
  </div>

  <!-- Multi-Channel Info -->
  <div class="bg-orange-50 rounded-lg p-6 border border-orange-200">
    <div class="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center mb-4">
      <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
      </svg>
    </div>
    <h3 class="text-lg font-semibold text-orange-900 mb-2">Multi-Channel</h3>
    <p class="text-orange-700 text-sm">Coordinated campaigns across email, social, and SEO for maximum impact.</p>
  </div>
</div>
