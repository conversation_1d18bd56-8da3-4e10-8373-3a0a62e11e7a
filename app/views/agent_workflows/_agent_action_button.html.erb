<% agent_enabled = campaign.can_generate_ai_content? %>
<% has_content = case agent_type
                  when 'email'
                    campaign.email_campaign&.content.present?
                  when 'social'
                    campaign.social_campaign&.content_variants&.any?
                  when 'seo'
                    campaign.seo_campaign&.content_strategy&.any?
                  else
                    false
                  end %>

<% if agent_enabled %>
  <%= button_to generate_content_campaign_agent_workflows_path(campaign), 
                params: { agent_type: agent_type },
                method: :post,
                remote: true,
                data: { turbo: true },
                class: "inline-flex items-center px-3 py-2 border border-transparent text-xs font-medium rounded text-white #{has_content ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" do %>
    <% if has_content %>
      🔄 Regenerate
    <% else %>
      ✨ Generate
    <% end %>
  <% end %>
<% else %>
  <span class="inline-flex items-center px-3 py-2 border border-gray-300 text-xs font-medium rounded text-gray-500 bg-gray-100 cursor-not-allowed">
    <% if has_content %>
      ✅ Generated
    <% else %>
      ⏸️ Disabled
    <% end %>
  </span>
<% end %>
