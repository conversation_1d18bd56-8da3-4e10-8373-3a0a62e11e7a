<!DOCTYPE html>
<html lang="en">
  <head>
    <title>AI Marketing Hub<%= " - #{yield(:title)}" if content_for?(:title) %></title>
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <meta name="description" content="Transform your marketing with AI-powered automation. Create campaigns, analyze data, and grow your business 10x faster with our enterprise-grade platform.">
    <meta name="keywords" content="AI marketing, marketing automation, campaigns, analytics, business growth, lead generation, ROI optimization">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="AI Marketing Hub - Transform Your Marketing with AI">
    <meta property="og:description" content="Automate campaigns, optimize performance, and scale your business with our AI-powered marketing platform. Get 3x more leads in 30 days.">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<%= request.original_url %>">
    <meta property="og:image" content="<%= og_image_url %>">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="AI Marketing Hub - Transform Your Marketing with AI">
    <meta name="twitter:description" content="Automate campaigns, optimize performance, and scale your business with our AI-powered marketing platform.">
    <meta name="twitter:image" content="<%= twitter_image_url %>">
    
    <!-- Canonical URL -->
    <link rel="canonical" href="<%= request.original_url %>">
    
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    
    <!-- Stylesheets -->
    <%= stylesheet_link_tag "tailwind", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "landing_page", "data-turbo-track": "reload" %>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg">
    <link rel="icon" type="image/png" href="/favicon.png">
    
    <!-- Theme Color -->
    <meta name="theme-color" content="#3B82F6">
    <meta name="msapplication-TileColor" content="#3B82F6">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "AI Marketing Hub",
      "description": "AI-powered marketing automation platform for businesses",
      "applicationCategory": "BusinessApplication",
      "operatingSystem": "Web",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD",
        "description": "Free trial available"
      },
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.9",
        "ratingCount": "10000"
      }
    }
    </script>
    
    <%= javascript_importmap_tags %>
    
    <!-- Critical CSS inline for performance -->
    <style>
      /* Critical above-the-fold styles */
      .hero-section {
        min-height: 100vh;
        background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #e0e7ff 100%);
      }
      
      .gradient-text {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .glass-effect {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.18);
      }
      
      /* Prevent layout shift */
      .floating-element {
        will-change: transform;
      }
      
      /* Loading state */
      .page-loading {
        opacity: 0;
        transition: opacity 0.3s ease-in-out;
      }
      
      .page-loaded {
        opacity: 1;
      }
    </style>
  </head>

  <body class="bg-white antialiased page-loading">
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 bg-blue-600 text-white px-4 py-2 z-50">
      Skip to main content
    </a>
    
    <!-- Shared Navigation -->
    <%= render 'shared/navbar' %>
    
    <!-- Flash Messages -->
    <%= render 'shared/flash_messages' %>

    <!-- Main Content -->
    <main id="main-content" class="relative">
      <%= yield %>
    </main>

    <!-- Shared Footer -->
    <%= render 'shared/footer' %>
    
    <!-- JavaScript -->
    <%= javascript_include_tag "landing_page", "data-turbo-track": "reload", defer: true %>
    
    <!-- Performance and Analytics Scripts -->
    <script>
      // Page load performance
      window.addEventListener('load', function() {
        document.body.classList.add('page-loaded');
        document.body.classList.remove('page-loading');
        
        // Report page load time
        if ('performance' in window && 'getEntriesByType' in performance) {
          const navTiming = performance.getEntriesByType('navigation')[0];
          if (navTiming) {
            const loadTime = navTiming.loadEventEnd - navTiming.loadEventStart;
            console.log('Page loaded in:', loadTime.toFixed(2), 'ms');
          }
        }
      });
      
      // Preload critical resources
      function preloadCriticalResources() {
        const criticalImages = [
          '/assets/hero-dashboard-preview.webp',
          '/assets/testimonial-avatars.webp'
        ];
        
        criticalImages.forEach(src => {
          const link = document.createElement('link');
          link.rel = 'preload';
          link.as = 'image';
          link.href = src;
          document.head.appendChild(link);
        });
      }
      
      // Initialize on DOM ready
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', preloadCriticalResources);
      } else {
        preloadCriticalResources();
      }
    </script>
    
    <!-- Service Worker for PWA capabilities -->
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/service-worker.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
    
    <!-- Hotjar or Analytics Scripts (conditionally loaded) -->
    <% unless Rails.env.development? %>
      <!-- Add your analytics scripts here -->
      <!-- Example: Google Analytics, Hotjar, etc. -->
    <% end %>
  </body>
</html>
