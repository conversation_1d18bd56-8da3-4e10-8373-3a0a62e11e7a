<% content_for :title, "Sign In" %>

<!-- Sign In Form -->
<div class="space-y-6">
  <!-- Header -->
  <div class="text-center">
    <h2 class="text-3xl font-bold text-gray-900">Welcome back</h2>
    <p class="mt-2 text-gray-600">Sign in to your AI Marketing Hub account</p>
  </div>

  <!-- Sign In Form -->
  <%= form_with(model: resource, as: resource_name, url: session_path(resource_name), local: true, class: "space-y-6") do |form| %>
    
    <!-- Email Field -->
    <div class="space-y-2">
      <%= form.label :email, class: "block text-sm font-medium text-gray-700" %>
      <div class="relative">
        <%= form.email_field :email, autofocus: true, autocomplete: "email", 
            class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12",
            placeholder: "Enter your email address" %>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Password Field -->
    <div class="space-y-2">
      <%= form.label :password, class: "block text-sm font-medium text-gray-700" %>
      <div class="relative">
        <%= form.password_field :password, autocomplete: "current-password",
            class: "block w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200 pl-12 pr-12",
            placeholder: "Enter your password" %>
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
          </svg>
        </div>
        <!-- Password Toggle Button -->
        <button type="button" id="password-toggle" class="absolute inset-y-0 right-0 pr-3 flex items-center">
          <svg id="password-show" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          <svg id="password-hide" class="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L12 12m3.121-3.121a3 3 0 00-4.243-4.243m4.243 4.243L21 21"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Remember Me & Forgot Password -->
    <div class="flex items-center justify-between">
      <% if devise_mapping.rememberable? %>
        <div class="flex items-center">
          <%= form.check_box :remember_me, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200" %>
          <%= form.label :remember_me, class: "ml-2 block text-sm text-gray-700 cursor-pointer" %>
        </div>
      <% end %>

      <div class="text-sm">
        <%= link_to "Forgot password?", new_password_path(resource_name), 
            class: "font-medium text-blue-600 hover:text-blue-500 transition-colors duration-200" %>
      </div>
    </div>

    <!-- Submit Button -->
    <%= form.submit "Sign In", 
        class: "group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transform hover:-translate-y-0.5 transition-all duration-200 shadow-lg hover:shadow-xl" %>
  <% end %>

  <!-- Divider -->
  <div class="relative">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-gray-300"></div>
    </div>
    <div class="relative flex justify-center text-sm">
      <span class="px-2 bg-gray-50 text-gray-500">Don't have an account?</span>
    </div>
  </div>

  <!-- Sign Up Link -->
  <div class="text-center">
    <%= link_to  new_registration_path(resource_name), 
        class: "group relative inline-flex items-center justify-center py-3 px-6 border border-gray-300 text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200" do %>
      <span>Create your account</span>
      <svg class="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
      </svg>
    <% end %>
  </div>

  <!-- Back to Home -->
  <div class="text-center">
    <%= link_to "← Back to Home", root_path, 
        class: "text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200" %>
  </div>
</div>

<!-- JavaScript for Password Toggle -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  const passwordField = document.getElementById('<%= "#{resource_name}_password" %>');
  const passwordToggle = document.getElementById('password-toggle');
  const showIcon = document.getElementById('password-show');
  const hideIcon = document.getElementById('password-hide');
  
  if (passwordToggle && passwordField) {
    passwordToggle.addEventListener('click', function() {
      if (passwordField.type === 'password') {
        passwordField.type = 'text';
        showIcon.classList.add('hidden');
        hideIcon.classList.remove('hidden');
      } else {
        passwordField.type = 'password';
        showIcon.classList.remove('hidden');
        hideIcon.classList.add('hidden');
      }
    });
  }
});
</script>