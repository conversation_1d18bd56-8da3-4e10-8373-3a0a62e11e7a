# frozen_string_literal: true

# Social Media Image Generator Service
# Usage: SocialImageGeneratorService.new.generate_all
class SocialImageGeneratorService
  CANVAS_TEMPLATE = <<~HTML
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
            body { margin: 0; font-family: 'Inter', sans-serif; }
            .og-image {
                width: 1200px;
                height: 630px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
            }
            .twitter-image {
                width: 1200px;
                height: 600px;
                background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
            }
            .content {
                text-align: center;
                color: white;
                z-index: 2;
                padding: 60px;
            }
            .title {
                font-size: 48px;
                font-weight: 700;
                margin-bottom: 20px;
                line-height: 1.2;
            }
            .subtitle {
                font-size: 24px;
                font-weight: 400;
                opacity: 0.9;
                margin-bottom: 30px;
            }
            .badge {
                background: rgba(255, 255, 255, 0.2);
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 25px;
                padding: 8px 20px;
                font-size: 14px;
                font-weight: 600;
                display: inline-block;
                margin-bottom: 30px;
            }
            .geometric-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                opacity: 0.1;
                z-index: 1;
            }
        </style>
    </head>
    <body>
        <div class="%{image_class}">
            <div class="geometric-bg">
                <svg width="100%" height="100%" viewBox="0 0 1200 630">
                    <circle cx="200" cy="100" r="60" fill="rgba(255,255,255,0.1)"/>
                    <circle cx="1000" cy="200" r="80" fill="rgba(255,255,255,0.05)"/>
                    <circle cx="100" cy="500" r="40" fill="rgba(255,255,255,0.1)"/>
                    <circle cx="1100" cy="500" r="70" fill="rgba(255,255,255,0.05)"/>
                </svg>
            </div>
            <div class="content">
                <div class="badge">🚀 AI-Powered Marketing</div>
                <div class="title">AI Marketing Hub</div>
                <div class="subtitle">Transform Your Marketing with Enterprise-Grade AI Automation</div>
            </div>
        </div>
    </body>
    </html>
  HTML

  def initialize
    @assets_path = Rails.root.join('app', 'assets', 'images')
  end

  def generate_all
    generate_og_image
    generate_twitter_image
    puts "✅ Social media images generated successfully!"
    puts "📁 Images saved to: #{@assets_path}"
    puts "🌐 Open Graph: marketing-hub-og-image.jpg"
    puts "🐦 Twitter Card: marketing-hub-twitter-card.jpg"
  end

  private

  def generate_og_image
    html_content = CANVAS_TEMPLATE % { image_class: 'og-image' }
    save_html_as_image(html_content, 'marketing-hub-og-image.jpg', 1200, 630)
  end

  def generate_twitter_image
    html_content = CANVAS_TEMPLATE % { image_class: 'twitter-image' }
    save_html_as_image(html_content, 'marketing-hub-twitter-card.jpg', 1200, 600)
  end

  def save_html_as_image(html_content, filename, width, height)
    # Save HTML template for manual conversion
    html_filename = filename.gsub('.jpg', '.html')
    File.write(@assets_path.join(html_filename), html_content)
    
    puts "📝 HTML template saved: #{html_filename}"
    puts "🛠️  To convert to image, open #{html_filename} in browser and screenshot"
    puts "   or use a service like https://htmlcsstoimage.com/"
  end
end
