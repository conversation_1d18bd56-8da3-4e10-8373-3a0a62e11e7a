# frozen_string_literal: true

# VibeAnalysisService - AI-powered vibe analysis for campaigns using Ruby LLM
#
# This service provides comprehensive vibe analysis including:
# - Emotional resonance analysis using Plutchik's emotion wheel
# - Cultural moment detection and relevance scoring
# - Authenticity validation and brand alignment
# - Psychographic targeting optimization
class VibeAnalysisService
  include ActiveModel::Model
  include ActiveModel::Attributes

  # Configuration
  ANALYSIS_TYPES = %w[emotional cultural authenticity psychographic].freeze
  
  # Confidence thresholds
  HIGH_CONFIDENCE_THRESHOLD = 0.8
  MEDIUM_CONFIDENCE_THRESHOLD = 0.6
  LOW_CONFIDENCE_THRESHOLD = 0.4

  # Emotional analysis prompts
  EMOTIONAL_ANALYSIS_PROMPT = <<~PROMPT
    Analyze the emotional resonance and vibe of this marketing campaign content.
    
    Campaign Content:
    - Name: %{campaign_name}
    - Description: %{campaign_description}
    - Target Audience: %{target_audience}
    - Campaign Type: %{campaign_type}
    - Content: %{campaign_content}
    
    Please provide analysis in this JSON format:
    {
      "primary_emotion": "one of: joy, anger, fear, sadness, disgust, surprise, anticipation, trust",
      "secondary_emotions": ["array of 2-3 complementary emotions"],
      "emotional_intensity": "one of: subtle, moderate, strong, intense",
      "resonance_strength": 7.5,
      "confidence_score": 0.89,
      "emotional_journey": {
        "start_emotion": "emotion at beginning",
        "peak_emotion": "strongest emotional moment",
        "end_emotion": "concluding emotion"
      },
      "audience_alignment": {
        "demographic_fit": 8.2,
        "cultural_sensitivity": 9.1,
        "age_appropriateness": 8.7
      },
      "improvement_suggestions": [
        "specific actionable recommendations"
      ]
    }
  PROMPT

  CULTURAL_ANALYSIS_PROMPT = <<~PROMPT
    Analyze the cultural relevance and moment capitalization potential of this marketing campaign.
    
    Campaign Content:
    - Name: %{campaign_name}
    - Description: %{campaign_description}
    - Target Audience: %{target_audience}
    - Timing Context: %{timing_context}
    - Content: %{campaign_content}
    
    Please provide analysis in this JSON format:
    {
      "cultural_moments": [
        {
          "moment_type": "trending_topic|seasonal|event|social_movement",
          "relevance_score": 8.5,
          "timing_window": "immediate|short_term|long_term",
          "engagement_potential": 7.8
        }
      ],
      "cultural_sensitivity_score": 9.2,
      "trend_alignment": {
        "current_trends": ["identified trending elements"],
        "alignment_strength": 8.1,
        "trend_longevity": "short|medium|long"
      },
      "regional_considerations": {
        "global_appeal": 7.5,
        "regional_variations": ["considerations for different regions"],
        "localization_needs": ["areas needing cultural adaptation"]
      },
      "timing_optimization": {
        "optimal_launch_window": "time recommendation",
        "cultural_calendar_alignment": 8.7,
        "competitive_landscape": "analysis of cultural timing vs competitors"
      }
    }
  PROMPT

  AUTHENTICITY_ANALYSIS_PROMPT = <<~PROMPT
    Analyze the authenticity and brand alignment of this marketing campaign.
    
    Campaign Content:
    - Name: %{campaign_name}
    - Description: %{campaign_description}
    - Brand Context: %{brand_context}
    - Historical Campaigns: %{historical_context}
    - Content: %{campaign_content}
    
    Please provide analysis in this JSON format:
    {
      "authenticity_score": 8.4,
      "brand_voice_consistency": {
        "tone_alignment": 9.1,
        "vocabulary_consistency": 8.7,
        "personality_match": 8.9,
        "value_alignment": 9.3
      },
      "trust_factors": {
        "transparency_score": 8.8,
        "credibility_indicators": ["specific credible elements"],
        "potential_skepticism_triggers": ["elements that might raise doubt"]
      },
      "authenticity_risks": [
        {
          "risk_type": "brand_misalignment|cultural_insensitivity|audience_mismatch",
          "severity": "low|medium|high|critical",
          "description": "detailed risk description",
          "mitigation_strategy": "recommended action"
        }
      ],
      "enhancement_recommendations": [
        "specific ways to improve authenticity"
      ]
    }
  PROMPT

  PSYCHOGRAPHIC_ANALYSIS_PROMPT = <<~PROMPT
    Analyze the psychographic targeting and audience psychology of this marketing campaign.
    
    Campaign Content:
    - Name: %{campaign_name}
    - Description: %{campaign_description}
    - Target Audience: %{target_audience}
    - Audience Psychographics: %{psychographic_context}
    - Content: %{campaign_content}
    
    Please provide analysis in this JSON format:
    {
      "psychographic_alignment": {
        "values_match": 8.6,
        "lifestyle_fit": 7.9,
        "personality_resonance": 8.2,
        "motivation_triggers": ["identified psychological motivators"]
      },
      "behavioral_predictions": {
        "engagement_likelihood": 8.1,
        "sharing_probability": 7.4,
        "conversion_potential": 7.8,
        "loyalty_building_strength": 8.3
      },
      "psychological_triggers": [
        {
          "trigger_type": "fear_of_missing_out|social_proof|authority|scarcity|reciprocity",
          "strength": 8.2,
          "application": "how it's used in the campaign"
        }
      ],
      "audience_segmentation": {
        "primary_segment_fit": 9.1,
        "secondary_segments": ["other segments that might respond"],
        "segment_specific_messaging": ["tailored approaches for different segments"]
      }
    }
  PROMPT

  attr_accessor :campaign, :analysis_type, :options

  def initialize(campaign:, analysis_type: 'emotional', options: {})
    @campaign = campaign
    @analysis_type = analysis_type.to_s
    @options = options.with_indifferent_access
    @ruby_llm_service = RubyLlmService.new
  end

  # Main analysis method
  def analyze
    return error_result("Invalid analysis type") unless valid_analysis_type?
    return error_result("Campaign not found") unless campaign.present?

    begin
      analysis_result = perform_analysis
      
      if analysis_result[:success]
        store_analysis_result(analysis_result)
        analysis_result
      else
        error_result(analysis_result[:error])
      end
    rescue StandardError => e
      Rails.logger.error "VibeAnalysisService error: #{e.message}"
      error_result("Analysis failed: #{e.message}")
    end
  end

  # Batch analysis for multiple types
  def analyze_all_types
    results = {}
    
    ANALYSIS_TYPES.each do |type|
      service = self.class.new(campaign: campaign, analysis_type: type, options: options)
      results[type] = service.analyze
    end
    
    results
  end

  # Get existing analysis for campaign
  def self.get_existing_analysis(campaign, analysis_type = nil)
    scope = campaign.vibe_analysis_records
    scope = scope.where(analysis_type: analysis_type) if analysis_type
    scope.order(created_at: :desc)
  end

  # Compare with historical analysis
  def compare_with_historical
    return {} unless campaign.persisted?
    
    historical_analyses = self.class.get_existing_analysis(campaign, analysis_type).limit(5)
    return {} if historical_analyses.empty?
    
    current_result = analyze
    return current_result unless current_result[:success]
    
    build_historical_comparison(current_result, historical_analyses)
  end

  # Regenerate analysis if conditions changed
  def regenerate_if_needed
    latest_analysis = campaign.vibe_analysis_records
                             .where(analysis_type: analysis_type)
                             .order(created_at: :desc)
                             .first
    
    return analyze unless latest_analysis
    
    # Regenerate if campaign was updated after last analysis
    if campaign.updated_at > latest_analysis.created_at
      analyze
    else
      format_existing_result(latest_analysis)
    end
  end

  private

  def valid_analysis_type?
    ANALYSIS_TYPES.include?(analysis_type)
  end

  def perform_analysis
    case analysis_type
    when 'emotional'
      perform_emotional_analysis
    when 'cultural' 
      perform_cultural_analysis
    when 'authenticity'
      perform_authenticity_analysis
    when 'psychographic'
      perform_psychographic_analysis
    else
      { success: false, error: "Unknown analysis type: #{analysis_type}" }
    end
  end

  def perform_emotional_analysis
    prompt = format_prompt(EMOTIONAL_ANALYSIS_PROMPT)
    
    llm_result = @ruby_llm_service.generate_content(
      prompt: prompt,
      model: options[:model] || 'gpt-4',
      max_tokens: 1000,
      temperature: 0.3
    )
    
    return { success: false, error: llm_result[:error] } unless llm_result[:success]
    
    begin
      analysis_data = JSON.parse(llm_result[:content])
      
      {
        success: true,
        analysis_type: 'emotional',
        analysis_data: analysis_data,
        confidence_score: analysis_data['confidence_score'] || 0.7,
        vibe_score: analysis_data['resonance_strength'] || 0.0,
        metadata: {
          model_used: llm_result[:model_used],
          processing_time: llm_result[:processing_time],
          analysis_version: '1.0'
        }
      }
    rescue JSON::ParserError => e
      { success: false, error: "Failed to parse LLM response: #{e.message}" }
    end
  end

  def perform_cultural_analysis
    prompt = format_prompt(CULTURAL_ANALYSIS_PROMPT)
    
    llm_result = @ruby_llm_service.generate_content(
      prompt: prompt,
      model: options[:model] || 'gpt-4',
      max_tokens: 1200,
      temperature: 0.4
    )
    
    return { success: false, error: llm_result[:error] } unless llm_result[:success]
    
    begin
      analysis_data = JSON.parse(llm_result[:content])
      
      # Calculate overall cultural score
      cultural_score = calculate_cultural_vibe_score(analysis_data)
      
      {
        success: true,
        analysis_type: 'cultural',
        analysis_data: analysis_data,
        confidence_score: calculate_cultural_confidence(analysis_data),
        vibe_score: cultural_score,
        metadata: {
          model_used: llm_result[:model_used],
          processing_time: llm_result[:processing_time],
          analysis_version: '1.0'
        }
      }
    rescue JSON::ParserError => e
      { success: false, error: "Failed to parse cultural analysis: #{e.message}" }
    end
  end

  def perform_authenticity_analysis
    prompt = format_prompt(AUTHENTICITY_ANALYSIS_PROMPT)
    
    llm_result = @ruby_llm_service.generate_content(
      prompt: prompt,
      model: options[:model] || 'gpt-4',
      max_tokens: 1100,
      temperature: 0.2 # Lower temperature for consistency analysis
    )
    
    return { success: false, error: llm_result[:error] } unless llm_result[:success]
    
    begin
      analysis_data = JSON.parse(llm_result[:content])
      
      {
        success: true,
        analysis_type: 'authenticity',
        analysis_data: analysis_data,
        confidence_score: calculate_authenticity_confidence(analysis_data),
        vibe_score: analysis_data['authenticity_score'] || 0.0,
        metadata: {
          model_used: llm_result[:model_used],
          processing_time: llm_result[:processing_time],
          analysis_version: '1.0'
        }
      }
    rescue JSON::ParserError => e
      { success: false, error: "Failed to parse authenticity analysis: #{e.message}" }
    end
  end

  def perform_psychographic_analysis
    prompt = format_prompt(PSYCHOGRAPHIC_ANALYSIS_PROMPT)
    
    llm_result = @ruby_llm_service.generate_content(
      prompt: prompt,
      model: options[:model] || 'claude-3-sonnet',
      max_tokens: 1300,
      temperature: 0.5
    )
    
    return { success: false, error: llm_result[:error] } unless llm_result[:success]
    
    begin
      analysis_data = JSON.parse(llm_result[:content])
      
      # Calculate psychographic vibe score
      psychographic_score = calculate_psychographic_vibe_score(analysis_data)
      
      {
        success: true,
        analysis_type: 'psychographic',
        analysis_data: analysis_data,
        confidence_score: calculate_psychographic_confidence(analysis_data),
        vibe_score: psychographic_score,
        metadata: {
          model_used: llm_result[:model_used],
          processing_time: llm_result[:processing_time],
          analysis_version: '1.0'
        }
      }
    rescue JSON::ParserError => e
      { success: false, error: "Failed to parse psychographic analysis: #{e.message}" }
    end
  end

  def format_prompt(template)
    template % {
      campaign_name: campaign.name,
      campaign_description: campaign.description || "No description provided",
      target_audience: campaign.target_audience,
      campaign_type: campaign.campaign_type.humanize,
      campaign_content: extract_campaign_content,
      timing_context: generate_timing_context,
      brand_context: generate_brand_context,
      historical_context: generate_historical_context,
      psychographic_context: generate_psychographic_context
    }
  end

  def extract_campaign_content
    content_parts = []
    
    # Extract content from specialized campaigns
    if campaign.email_campaign
      content_parts << "Email Subject: #{campaign.email_campaign.subject}"
      content_parts << "Email Content: #{campaign.email_campaign.content}"
    end
    
    if campaign.social_campaign
      content_parts << "Social Content: #{campaign.social_campaign.content}"
      content_parts << "Hashtags: #{campaign.social_campaign.hashtags}"
    end
    
    if campaign.seo_campaign
      content_parts << "SEO Keywords: #{campaign.seo_campaign.keywords}"
      content_parts << "Meta Description: #{campaign.seo_campaign.meta_description}"
    end
    
    # Fallback to campaign settings
    if content_parts.empty?
      settings_content = campaign.settings.dig('content') || {}
      content_parts << "Campaign Settings: #{settings_content.to_json}"
    end
    
    content_parts.join("\n")
  end

  def generate_timing_context
    context = []
    context << "Current Date: #{Date.current.strftime('%B %d, %Y')}"
    context << "Season: #{determine_season}"
    context << "Campaign Start: #{campaign.start_date}" if campaign.start_date
    context << "Campaign End: #{campaign.end_date}" if campaign.end_date
    
    # Add trending cultural moments if available
    trending_moments = CulturalMoment.currently_trending.limit(3)
    if trending_moments.any?
      context << "Current Trending Moments: #{trending_moments.pluck(:name).join(', ')}"
    end
    
    context.join("\n")
  end

  def generate_brand_context
    # This would ideally come from a brand profile
    # For now, extract from tenant or campaign data
    brand_info = []
    brand_info << "Tenant: #{campaign.tenant.name}"
    
    # Look for brand voice in campaign settings
    brand_voice = campaign.settings.dig('brand_voice') || {}
    brand_info << "Brand Voice: #{brand_voice.to_json}" if brand_voice.any?
    
    brand_info.join("\n")
  end

  def generate_historical_context
    recent_campaigns = campaign.tenant.campaigns
                               .where.not(id: campaign.id)
                               .includes(:vibe_analysis_records)
                               .limit(3)
    
    return "No historical campaigns available" if recent_campaigns.empty?
    
    historical_info = recent_campaigns.map do |camp|
      vibe_score = camp.vibe_analysis_records.last&.vibe_score || "N/A"
      "Campaign: #{camp.name} (Vibe Score: #{vibe_score})"
    end
    
    "Recent Campaigns:\n#{historical_info.join("\n")}"
  end

  def generate_psychographic_context
    # Extract psychographic info from target audience and settings
    psychographic_data = campaign.settings.dig('psychographics') || {}
    
    if psychographic_data.any?
      psychographic_data.to_json
    else
      "Target Audience: #{campaign.target_audience}\nNo specific psychographic data available"
    end
  end

  def determine_season
    month = Date.current.month
    case month
    when 12, 1, 2 then 'Winter'
    when 3, 4, 5 then 'Spring'
    when 6, 7, 8 then 'Summer'
    when 9, 10, 11 then 'Autumn'
    end
  end

  def calculate_cultural_vibe_score(analysis_data)
    cultural_sensitivity = analysis_data.dig('cultural_sensitivity_score') || 0.0
    trend_alignment = analysis_data.dig('trend_alignment', 'alignment_strength') || 0.0
    timing_score = analysis_data.dig('timing_optimization', 'cultural_calendar_alignment') || 0.0
    
    # Weight the components
    ((cultural_sensitivity * 0.4) + (trend_alignment * 0.4) + (timing_score * 0.2)).round(2)
  end

  def calculate_cultural_confidence(analysis_data)
    # Base confidence on the presence and quality of cultural analysis
    base_confidence = 0.7
    
    # Increase confidence if multiple cultural moments identified
    moments_count = analysis_data.dig('cultural_moments')&.size || 0
    confidence_boost = [moments_count * 0.05, 0.2].min
    
    [base_confidence + confidence_boost, 1.0].min.round(2)
  end

  def calculate_authenticity_confidence(analysis_data)
    # Higher confidence for authenticity if brand voice data is comprehensive
    brand_voice_data = analysis_data.dig('brand_voice_consistency') || {}
    brand_elements = brand_voice_data.keys.size
    
    base_confidence = 0.75
    element_boost = [brand_elements * 0.03, 0.15].min
    
    [base_confidence + element_boost, 1.0].min.round(2)
  end

  def calculate_psychographic_vibe_score(analysis_data)
    alignment_data = analysis_data.dig('psychographic_alignment') || {}
    behavioral_data = analysis_data.dig('behavioral_predictions') || {}
    
    alignment_avg = alignment_data.values.compact.sum / [alignment_data.values.compact.size, 1].max
    behavioral_avg = behavioral_data.values.compact.sum / [behavioral_data.values.compact.size, 1].max
    
    ((alignment_avg + behavioral_avg) / 2).round(2)
  end

  def calculate_psychographic_confidence(analysis_data)
    # Confidence based on depth of psychographic analysis
    triggers_count = analysis_data.dig('psychological_triggers')&.size || 0
    segments_count = analysis_data.dig('audience_segmentation', 'secondary_segments')&.size || 0
    
    base_confidence = 0.7
    analysis_depth_boost = [(triggers_count + segments_count) * 0.02, 0.2].min
    
    [base_confidence + analysis_depth_boost, 1.0].min.round(2)
  end

  def store_analysis_result(result)
    return unless result[:success]
    
    VibeAnalysisRecord.create!(
      campaign: campaign,
      tenant: campaign.tenant,
      analysis_type: result[:analysis_type],
      analysis_data: result[:analysis_data],
      confidence_score: result[:confidence_score],
      vibe_score: result[:vibe_score],
      metadata: result[:metadata]
    )
  end

  def build_historical_comparison(current_result, historical_analyses)
    return current_result unless current_result[:success]
    
    historical_scores = historical_analyses.pluck(:vibe_score)
    historical_avg = historical_scores.sum / [historical_scores.size, 1].max
    
    current_score = current_result[:vibe_score]
    
    comparison_data = {
      current_score: current_score,
      historical_average: historical_avg.round(2),
      trend: current_score > historical_avg ? 'improving' : 'declining',
      improvement_percentage: calculate_improvement_percentage(current_score, historical_avg),
      historical_count: historical_scores.size
    }
    
    current_result.merge(historical_comparison: comparison_data)
  end

  def calculate_improvement_percentage(current, historical_avg)
    return 0.0 if historical_avg.zero?
    
    ((current - historical_avg) / historical_avg * 100).round(2)
  end

  def format_existing_result(analysis_record)
    {
      success: true,
      analysis_type: analysis_record.analysis_type,
      analysis_data: analysis_record.analysis_data,
      confidence_score: analysis_record.confidence_score,
      vibe_score: analysis_record.vibe_score,
      cached: true,
      created_at: analysis_record.created_at
    }
  end

  def error_result(message)
    {
      success: false,
      error: message,
      analysis_type: analysis_type
    }
  end
end
