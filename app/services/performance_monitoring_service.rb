# frozen_string_literal: true

##
# Performance Monitoring Service
#
# Monitors database query performance, cache hit rates, and response times
# to track the effectiveness of our optimization efforts.
#
# @example Basic usage
#   monitor = PerformanceMonitoringService.new
#   monitor.track_query_performance('campaigns_index') do
#     # Execute database queries
#   end
#
class PerformanceMonitoringService
  attr_reader :metrics

  def initialize
    @metrics = {}
  end

  ##
  # Track query performance for a specific operation
  #
  # @param operation_name [String] Name of the operation being tracked
  # @yield Block containing the database operations to monitor
  # @return [Object] Result of the yielded block
  #
  def track_query_performance(operation_name)
    start_time = Time.current
    query_count_before = query_count
    
    result = yield
    
    end_time = Time.current
    query_count_after = query_count
    
    record_performance_metrics(operation_name, {
      duration: (end_time - start_time) * 1000, # Convert to milliseconds
      query_count: query_count_after - query_count_before,
      timestamp: start_time
    })
    
    result
  end

  ##
  # Track cache performance for a specific operation
  #
  # @param operation_name [String] Name of the operation being tracked
  # @param cache_key [String] Cache key being accessed
  # @param hit [Boolean] Whether the cache was hit or missed
  #
  def track_cache_performance(operation_name, cache_key, hit)
    cache_metrics = @metrics[:cache] ||= {}
    operation_metrics = cache_metrics[operation_name] ||= { hits: 0, misses: 0, keys: [] }
    
    if hit
      operation_metrics[:hits] += 1
    else
      operation_metrics[:misses] += 1
    end
    
    operation_metrics[:keys] << cache_key unless operation_metrics[:keys].include?(cache_key)
    
    Rails.logger.info "Cache #{hit ? 'HIT' : 'MISS'} for #{operation_name}: #{cache_key}"
  end

  ##
  # Get performance summary for all tracked operations
  #
  # @return [Hash] Performance summary with averages and totals
  #
  def performance_summary
    {
      query_performance: query_performance_summary,
      cache_performance: cache_performance_summary,
      recommendations: performance_recommendations
    }
  end

  ##
  # Get cache hit rate for a specific operation
  #
  # @param operation_name [String] Name of the operation
  # @return [Float] Cache hit rate as a percentage
  #
  def cache_hit_rate(operation_name)
    cache_metrics = @metrics.dig(:cache, operation_name)
    return 0.0 unless cache_metrics
    
    total_requests = cache_metrics[:hits] + cache_metrics[:misses]
    return 0.0 if total_requests.zero?
    
    (cache_metrics[:hits].to_f / total_requests * 100).round(2)
  end

  ##
  # Check if an operation meets performance thresholds
  #
  # @param operation_name [String] Name of the operation
  # @return [Hash] Performance status and recommendations
  #
  def performance_status(operation_name)
    query_metrics = @metrics.dig(:queries, operation_name)
    cache_hit_rate = cache_hit_rate(operation_name)
    
    status = {
      operation: operation_name,
      healthy: true,
      issues: [],
      recommendations: []
    }
    
    if query_metrics
      avg_duration = query_metrics[:durations].sum / query_metrics[:durations].length
      avg_query_count = query_metrics[:query_counts].sum / query_metrics[:query_counts].length
      
      # Check performance thresholds
      if avg_duration > 1000 # More than 1 second
        status[:healthy] = false
        status[:issues] << "High response time: #{avg_duration.round(2)}ms"
        status[:recommendations] << "Consider adding database indexes or optimizing queries"
      end
      
      if avg_query_count > 10 # More than 10 queries
        status[:healthy] = false
        status[:issues] << "High query count: #{avg_query_count.round(1)} queries"
        status[:recommendations] << "Consider using eager loading or reducing N+1 queries"
      end
    end
    
    if cache_hit_rate < 80.0 # Less than 80% cache hit rate
      status[:healthy] = false
      status[:issues] << "Low cache hit rate: #{cache_hit_rate}%"
      status[:recommendations] << "Consider warming cache or adjusting cache TTL"
    end
    
    status
  end

  ##
  # Log performance metrics to Rails logger
  #
  # @param operation_name [String] Name of the operation
  #
  def log_performance_metrics(operation_name)
    status = performance_status(operation_name)
    
    if status[:healthy]
      Rails.logger.info "Performance OK for #{operation_name}"
    else
      Rails.logger.warn "Performance issues for #{operation_name}: #{status[:issues].join(', ')}"
      Rails.logger.info "Recommendations: #{status[:recommendations].join(', ')}"
    end
  end

  ##
  # Reset all performance metrics
  #
  def reset_metrics
    @metrics = {}
  end

  ##
  # Export metrics for external monitoring systems
  #
  # @return [Hash] Formatted metrics for export
  #
  def export_metrics
    {
      timestamp: Time.current.iso8601,
      query_metrics: @metrics[:queries] || {},
      cache_metrics: @metrics[:cache] || {},
      summary: performance_summary
    }
  end

  private

  ##
  # Get current query count from ActiveRecord
  #
  # @return [Integer] Current query count
  #
  def query_count
    if defined?(ActiveRecord::QueryCounter)
      ActiveRecord::QueryCounter.query_count
    else
      # Fallback if query counter is not available
      0
    end
  end

  ##
  # Record performance metrics for an operation
  #
  # @param operation_name [String] Name of the operation
  # @param metrics [Hash] Metrics to record
  #
  def record_performance_metrics(operation_name, metrics)
    query_metrics = @metrics[:queries] ||= {}
    operation_metrics = query_metrics[operation_name] ||= {
      durations: [],
      query_counts: [],
      timestamps: []
    }
    
    operation_metrics[:durations] << metrics[:duration]
    operation_metrics[:query_counts] << metrics[:query_count]
    operation_metrics[:timestamps] << metrics[:timestamp]
    
    # Keep only the last 100 measurements to prevent memory bloat
    if operation_metrics[:durations].length > 100
      operation_metrics[:durations] = operation_metrics[:durations].last(100)
      operation_metrics[:query_counts] = operation_metrics[:query_counts].last(100)
      operation_metrics[:timestamps] = operation_metrics[:timestamps].last(100)
    end
    
    Rails.logger.info "Query performance for #{operation_name}: #{metrics[:duration].round(2)}ms, #{metrics[:query_count]} queries"
  end

  ##
  # Generate query performance summary
  #
  # @return [Hash] Query performance summary
  #
  def query_performance_summary
    return {} unless @metrics[:queries]
    
    summary = {}
    
    @metrics[:queries].each do |operation, metrics|
      avg_duration = metrics[:durations].sum / metrics[:durations].length
      avg_query_count = metrics[:query_counts].sum / metrics[:query_counts].length
      
      summary[operation] = {
        average_duration_ms: avg_duration.round(2),
        average_query_count: avg_query_count.round(1),
        total_measurements: metrics[:durations].length,
        last_measured: metrics[:timestamps].last
      }
    end
    
    summary
  end

  ##
  # Generate cache performance summary
  #
  # @return [Hash] Cache performance summary
  #
  def cache_performance_summary
    return {} unless @metrics[:cache]
    
    summary = {}
    
    @metrics[:cache].each do |operation, metrics|
      total_requests = metrics[:hits] + metrics[:misses]
      hit_rate = total_requests > 0 ? (metrics[:hits].to_f / total_requests * 100) : 0.0
      
      summary[operation] = {
        hit_rate_percent: hit_rate.round(2),
        total_hits: metrics[:hits],
        total_misses: metrics[:misses],
        unique_keys: metrics[:keys].length
      }
    end
    
    summary
  end

  ##
  # Generate performance recommendations
  #
  # @return [Array<String>] List of performance recommendations
  #
  def performance_recommendations
    recommendations = []
    
    # Analyze query performance
    if @metrics[:queries]
      @metrics[:queries].each do |operation, metrics|
        avg_duration = metrics[:durations].sum / metrics[:durations].length
        avg_query_count = metrics[:query_counts].sum / metrics[:query_counts].length
        
        if avg_duration > 500
          recommendations << "#{operation}: Consider optimizing slow queries (#{avg_duration.round(2)}ms average)"
        end
        
        if avg_query_count > 5
          recommendations << "#{operation}: Consider reducing query count (#{avg_query_count.round(1)} average)"
        end
      end
    end
    
    # Analyze cache performance
    if @metrics[:cache]
      @metrics[:cache].each do |operation, metrics|
        hit_rate = cache_hit_rate(operation)
        
        if hit_rate < 70.0
          recommendations << "#{operation}: Improve cache hit rate (currently #{hit_rate}%)"
        end
      end
    end
    
    recommendations
  end
end
