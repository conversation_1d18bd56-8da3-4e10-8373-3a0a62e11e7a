# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EmailCampaign, type: :model do
  describe 'validations' do
    subject { build(:email_campaign) }

    it { should validate_presence_of(:subject_line) }
    it { should validate_presence_of(:content) }
    it { should validate_presence_of(:from_name) }
    it { should validate_presence_of(:from_email) }
    
    it 'validates email format for from_email' do
      email_campaign = build(:email_campaign, from_email: 'invalid-email')
      expect(email_campaign).not_to be_valid
      expect(email_campaign.errors[:from_email]).to include('must be a valid email address')
    end

    it 'validates subject line length' do
      email_campaign = build(:email_campaign, subject_line: 'a' * 151)
      expect(email_campaign).not_to be_valid
      expect(email_campaign.errors[:subject_line]).to include('is too long (maximum is 150 characters)')
    end
  end

  describe 'associations' do
    it { should belong_to(:campaign) }
    it 'inherits tenant through campaign' do
      email_campaign = create(:email_campaign)
      expect(email_campaign.tenant).to eq(email_campaign.campaign.tenant)
    end
  end

  describe 'scopes' do
    let(:tenant) { create(:tenant) }
    
    before do
      ActsAsTenant.current_tenant = tenant
    end

    it 'finds campaigns ready to send' do
      ready_campaign = create(:email_campaign, :ready_to_send)
      draft_campaign = create(:email_campaign)
      
      # Debug: Let's see what the campaign status actually is
      expect(ready_campaign.campaign.active?).to be true
      expect(draft_campaign.campaign.draft?).to be true
      
      expect(EmailCampaign.ready_to_send).to include(ready_campaign)
      expect(EmailCampaign.ready_to_send).not_to include(draft_campaign)
    end
  end

  describe '#ready_to_send?' do
    it 'returns true when all required fields are present and campaign is active' do
      email_campaign = create(:email_campaign, :ready_to_send)
      expect(email_campaign.campaign.active?).to be true
      expect(email_campaign.ready_to_send?).to be true
    end

    it 'returns false when content is missing' do
      email_campaign = build(:email_campaign, content: nil)
      expect(email_campaign.ready_to_send?).to be false
    end

    it 'returns false when from_email is invalid' do
      email_campaign = build(:email_campaign, from_email: 'invalid')
      expect(email_campaign.ready_to_send?).to be false
    end

    it 'returns false when campaign is not active' do
      email_campaign = create(:email_campaign) # Creates with draft campaign
      expect(email_campaign.campaign.draft?).to be true
      expect(email_campaign.ready_to_send?).to be false
    end
  end

  describe '#preview_snippet' do
    it 'returns first 100 characters of content' do
      long_content = 'a' * 150
      email_campaign = build(:email_campaign, content: long_content)
      expect(email_campaign.preview_snippet).to eq('a' * 100 + '...')
    end

    it 'returns full content if under 100 characters' do
      short_content = 'Short email content'
      email_campaign = build(:email_campaign, content: short_content)
      expect(email_campaign.preview_snippet).to eq(short_content)
    end
  end

  describe '#estimated_send_time' do
    it 'calculates send time based on recipient count' do
      email_campaign = build(:email_campaign)
      email_campaign.settings['recipient_count'] = 10000
      
      # Assuming 1000 emails per minute send rate
      expected_minutes = 10
      expect(email_campaign.estimated_send_time).to eq(expected_minutes)
    end

    it 'returns 1 minute minimum for small lists' do
      email_campaign = build(:email_campaign)
      email_campaign.settings['recipient_count'] = 100
      
      expect(email_campaign.estimated_send_time).to eq(1)
    end
  end

  describe 'content processing' do
    describe '#personalize_content_for' do
      it 'replaces merge tags with user data' do
        email_campaign = build(:email_campaign, 
                               content: 'Hello {{first_name}}, welcome to {{company_name}}!')
        user_data = { first_name: 'John', company_name: 'Acme Corp' }
        
        personalized = email_campaign.personalize_content_for(user_data)
        expect(personalized).to eq('Hello John, welcome to Acme Corp!')
      end

      it 'handles missing merge tags gracefully' do
        email_campaign = build(:email_campaign, content: 'Hello {{first_name}}!')
        user_data = {}
        
        personalized = email_campaign.personalize_content_for(user_data)
        expect(personalized).to eq('Hello !')
      end
    end
  end
end
