# frozen_string_literal: true

require 'rails_helper'

RSpec.describe SocialCampaign, type: :model do
  describe 'validations' do
    subject { build(:social_campaign) }

    it { should validate_presence_of(:platforms) }
    it { should validate_presence_of(:content_variants) }
    
    it 'validates at least one platform is selected' do
      social_campaign = build(:social_campaign, platforms: [])
      expect(social_campaign).not_to be_valid
      expect(social_campaign.errors[:platforms]).to include('must include at least one platform')
    end

    it 'validates platform names are supported' do
      social_campaign = build(:social_campaign, platforms: ['unsupported_platform'])
      expect(social_campaign).not_to be_valid
      expect(social_campaign.errors[:platforms]).to include('contains unsupported platforms')
    end

    it 'validates content exists for each platform' do
      social_campaign = build(:social_campaign, 
                              platforms: ['twitter', 'linkedin'],
                              content_variants: { 'twitter' => 'Twitter content' })
      expect(social_campaign).not_to be_valid
      expect(social_campaign.errors[:content_variants]).to include('must have content for all selected platforms')
    end

    it 'validates Twitter content length' do
      long_content = 'a' * 281 # Over Twitter limit
      social_campaign = build(:social_campaign,
                              platforms: ['twitter'],
                              content_variants: { 'twitter' => long_content })
      expect(social_campaign).not_to be_valid
      expect(social_campaign.errors[:content_variants]).to include('Twitter content exceeds 280 character limit')
    end
  end

  describe 'associations' do
    it { should belong_to(:campaign) }
    
    it 'inherits tenant through campaign' do
      social_campaign = create(:social_campaign)
      expect(social_campaign.tenant).to eq(social_campaign.campaign.tenant)
    end
  end

  describe 'scopes' do
    let(:tenant) { create(:tenant) }
    
    before do
      ActsAsTenant.current_tenant = tenant
    end

    it 'finds campaigns by platform' do
      twitter_campaign = create(:social_campaign, :twitter_only)
      linkedin_campaign = create(:social_campaign, :linkedin_only)
      
      expect(SocialCampaign.for_platform('twitter')).to include(twitter_campaign)
      expect(SocialCampaign.for_platform('twitter')).not_to include(linkedin_campaign)
    end

    it 'finds campaigns ready to post' do
      ready_campaign = create(:social_campaign, :ready_to_post)
      draft_campaign = create(:social_campaign)
      
      expect(SocialCampaign.ready_to_post).to include(ready_campaign)
      expect(SocialCampaign.ready_to_post).not_to include(draft_campaign)
    end
  end

  describe '#supported_platforms' do
    it 'returns the list of supported social platforms' do
      social_campaign = build(:social_campaign)
      expected_platforms = ['twitter', 'linkedin', 'facebook', 'instagram', 'youtube', 'tiktok']
      expect(social_campaign.supported_platforms).to eq(expected_platforms)
    end
  end

  describe '#ready_to_post?' do
    it 'returns true when campaign is active and has valid content' do
      social_campaign = create(:social_campaign, :ready_to_post)
      expect(social_campaign.ready_to_post?).to be true
    end

    it 'returns false when campaign is not active' do
      social_campaign = create(:social_campaign) # Draft campaign
      expect(social_campaign.ready_to_post?).to be false
    end

    it 'returns false when content is missing for platforms' do
      social_campaign = build(:social_campaign,
                              platforms: ['twitter', 'linkedin'],
                              content_variants: { 'twitter' => 'Only Twitter content' })
      expect(social_campaign.ready_to_post?).to be false
    end
  end

  describe '#content_for_platform' do
    it 'returns content for specified platform' do
      social_campaign = build(:social_campaign,
                              content_variants: { 
                                'twitter' => 'Twitter content',
                                'linkedin' => 'LinkedIn content'
                              })
      expect(social_campaign.content_for_platform('twitter')).to eq('Twitter content')
      expect(social_campaign.content_for_platform('linkedin')).to eq('LinkedIn content')
    end

    it 'returns nil for platforms without content' do
      social_campaign = build(:social_campaign, content_variants: {})
      expect(social_campaign.content_for_platform('twitter')).to be_nil
    end
  end

  describe '#character_count_for_platform' do
    it 'returns character count for platform content' do
      social_campaign = build(:social_campaign,
                              content_variants: { 'twitter' => 'Hello world!' })
      expect(social_campaign.character_count_for_platform('twitter')).to eq(12)
    end

    it 'returns 0 for platforms without content' do
      social_campaign = build(:social_campaign, content_variants: {})
      expect(social_campaign.character_count_for_platform('twitter')).to eq(0)
    end
  end

  describe '#scheduled_posts' do
    it 'returns scheduled posts from post_schedule' do
      schedule = {
        'posts' => [
          { 'platform' => 'twitter', 'scheduled_at' => 2.hours.from_now.iso8601 },
          { 'platform' => 'linkedin', 'scheduled_at' => 4.hours.from_now.iso8601 }
        ]
      }
      social_campaign = build(:social_campaign, post_schedule: schedule)
      expect(social_campaign.scheduled_posts.length).to eq(2)
    end

    it 'returns empty array when no posts scheduled' do
      social_campaign = build(:social_campaign, post_schedule: {})
      expect(social_campaign.scheduled_posts).to eq([])
    end
  end

  describe '#hashtag_list' do
    it 'returns array of hashtags' do
      social_campaign = build(:social_campaign, hashtags: '#marketing #automation #ai')
      expect(social_campaign.hashtag_list).to eq(['#marketing', '#automation', '#ai'])
    end

    it 'handles empty hashtags' do
      social_campaign = build(:social_campaign, hashtags: '')
      expect(social_campaign.hashtag_list).to eq([])
    end
  end

  describe '#estimated_reach' do
    it 'calculates estimated reach based on platforms and followers' do
      social_campaign = build(:social_campaign,
                              platforms: ['twitter', 'linkedin'],
                              social_settings: {
                                'follower_counts' => {
                                  'twitter' => 5000,
                                  'linkedin' => 2000
                                }
                              })
      # Assuming 10% engagement rate
      expect(social_campaign.estimated_reach).to eq(700) # (5000 + 2000) * 0.1
    end

    it 'returns 0 when no follower data available' do
      social_campaign = build(:social_campaign, social_settings: {})
      expect(social_campaign.estimated_reach).to eq(0)
    end
  end
end
