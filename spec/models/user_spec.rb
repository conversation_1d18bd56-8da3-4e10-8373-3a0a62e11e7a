# frozen_string_literal: true

require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'validations' do
    subject { build(:user) }

    it { should validate_presence_of(:email) }
    it { should validate_presence_of(:first_name) }
    it { should validate_presence_of(:last_name) }

    # Test email uniqueness manually since shoulda matcher has issues with tenant scoping
    it 'validates email uniqueness within tenant scope' do
      tenant1 = create(:tenant)
      tenant2 = create(:tenant)

      # Create user in tenant1
      user1 = nil
      ActsAsTenant.with_tenant(tenant1) do
        user1 = create(:user, email: '<EMAIL>')
      end

      # Same email should be allowed in different tenant
      user2 = nil
      ActsAsTenant.with_tenant(tenant2) do
        expect {
          user2 = create(:user, email: '<EMAIL>')
        }.not_to raise_error
      end

      expect(user1.tenant).to eq(tenant1)
      expect(user2.tenant).to eq(tenant2)
      expect(user1.email).to eq(user2.email)
    end

    it 'prevents duplicate emails within same tenant' do
      tenant = create(:tenant)
      
      ActsAsTenant.with_tenant(tenant) do
        create(:user, email: '<EMAIL>')
        
        expect {
          create(:user, email: '<EMAIL>')
        }.to raise_error(ActiveRecord::RecordInvalid, /Email has already been taken/)
      end
    end
  end

  describe 'associations' do
    it 'belongs to tenant' do
      user = build(:user)
      expect(user.tenant).to be_a(Tenant)
      expect(user.tenant_id).to be_present
    end
  end

  describe 'acts_as_tenant' do
    it 'should act as tenant' do
      expect(User.respond_to?(:acts_as_tenant)).to be true
    end
  end

  describe 'enums' do
    it { should define_enum_for(:role).with_values(%w[member admin owner]) }
  end

  describe '#full_name' do
    it 'returns the full name' do
      user = build(:user, first_name: 'John', last_name: 'Doe')
      expect(user.full_name).to eq('John Doe')
    end
  end

  describe '#admin_or_owner?' do
    it 'returns true for admin users' do
      user = build(:user, :admin)
      expect(user.admin_or_owner?).to be true
    end

    it 'returns true for owner users' do
      user = build(:user, :owner)
      expect(user.admin_or_owner?).to be true
    end

    it 'returns false for member users' do
      user = build(:user, role: 0) # member
      expect(user.admin_or_owner?).to be false
    end
  end
end
