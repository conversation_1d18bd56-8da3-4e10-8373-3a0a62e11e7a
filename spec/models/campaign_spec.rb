# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Campaign, type: :model do
  describe 'validations' do
    subject { build(:campaign) }

    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:campaign_type) }
    it { should validate_presence_of(:status) }
    it { should validate_presence_of(:target_audience) }
    
    it 'validates name uniqueness within tenant scope' do
      tenant1 = create(:tenant)
      tenant2 = create(:tenant)

      ActsAsTenant.with_tenant(tenant1) do
        create(:campaign, name: 'Holiday Sale Campaign')
      end

      # Same name should be allowed in different tenant
      ActsAsTenant.with_tenant(tenant2) do
        expect {
          create(:campaign, name: 'Holiday Sale Campaign')
        }.not_to raise_error
      end
    end

    it 'prevents duplicate campaign names within same tenant' do
      tenant = create(:tenant)
      
      ActsAsTenant.with_tenant(tenant) do
        create(:campaign, name: 'Duplicate Campaign')
        
        expect {
          create(:campaign, name: 'Duplicate Campaign')
        }.to raise_error(ActiveRecord::RecordInvalid, /Name has already been taken/)
      end
    end
  end

  describe 'associations' do
    it 'belongs to tenant' do
      campaign = build(:campaign)
      expect(campaign.tenant).to be_a(Tenant)
      expect(campaign.tenant_id).to be_present
    end

    it 'belongs to created_by user' do
      campaign = build(:campaign)
      expect(campaign.created_by).to be_a(User)
      
      # For persisted records, check the ID
      persisted_campaign = create(:campaign)
      expect(persisted_campaign.created_by_id).to be_present
    end

    # TODO: Enable these tests after creating the specialized models
    # it { should have_many(:campaign_metrics).dependent(:destroy) }
    # it { should have_one(:email_campaign).dependent(:destroy) }
    # it { should have_one(:social_campaign).dependent(:destroy) }
    # it { should have_one(:seo_campaign).dependent(:destroy) }
  end

  describe 'acts_as_tenant' do
    it 'should act as tenant' do
      expect(Campaign.respond_to?(:acts_as_tenant)).to be true
    end
  end

  describe 'enums' do
    it { should define_enum_for(:status).with_values(%w[draft active paused completed cancelled]) }
    it { should define_enum_for(:campaign_type).with_values(%w[email social seo multi_channel]) }
  end

  describe 'scopes' do
    let(:tenant) { create(:tenant) }
    
    before do
      ActsAsTenant.current_tenant = tenant
    end

    it 'filters by status' do
      active_campaign = create(:campaign, status: 'active')
      draft_campaign = create(:campaign, status: 'draft')
      
      expect(Campaign.active).to include(active_campaign)
      expect(Campaign.active).not_to include(draft_campaign)
    end

    it 'filters by campaign type' do
      email_campaign = create(:campaign, campaign_type: 'email')
      social_campaign = create(:campaign, campaign_type: 'social')
      
      expect(Campaign.email).to include(email_campaign)
      expect(Campaign.email).not_to include(social_campaign)
    end
  end

  describe '#can_be_activated?' do
    it 'returns true for draft campaigns' do
      campaign = build(:campaign, status: 'draft')
      expect(campaign.can_be_activated?).to be true
    end

    it 'returns false for completed campaigns' do
      campaign = build(:campaign, status: 'completed')
      expect(campaign.can_be_activated?).to be false
    end
  end

  describe '#duration_in_days' do
    it 'calculates duration when both dates are present' do
      campaign = build(:campaign, 
                      start_date: Date.current, 
                      end_date: Date.current + 7.days)
      expect(campaign.duration_in_days).to eq(7)
    end

    it 'returns nil when dates are missing' do
      campaign = build(:campaign, start_date: nil, end_date: nil)
      expect(campaign.duration_in_days).to be_nil
    end
  end

  describe '#progress_percentage' do
    it 'calculates progress for ongoing campaigns' do
      campaign = build(:campaign, 
                      start_date: 5.days.ago, 
                      end_date: 5.days.from_now,
                      status: 'active')
      expect(campaign.progress_percentage).to eq(50)
    end

    it 'returns 0 for campaigns not yet started' do
      campaign = build(:campaign, 
                      start_date: 1.day.from_now, 
                      end_date: 8.days.from_now,
                      status: 'draft')
      expect(campaign.progress_percentage).to eq(0)
    end

    it 'returns 100 for completed campaigns' do
      campaign = build(:campaign, status: 'completed')
      expect(campaign.progress_percentage).to eq(100)
    end
  end

  describe '#budget_in_dollars' do
    it 'converts cents to dollars' do
      campaign = build(:campaign, budget_cents: 15050)
      expect(campaign.budget_in_dollars).to eq(150.50)
    end
  end

  describe '#budget_in_dollars=' do
    it 'converts dollars to cents' do
      campaign = build(:campaign)
      campaign.budget_in_dollars = 250.75
      expect(campaign.budget_cents).to eq(25075)
    end
  end
end
