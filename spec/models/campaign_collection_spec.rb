# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CampaignCollection, type: :model do
  let(:tenant) { create(:tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe 'associations' do
    it { should belong_to(:tenant) }
    it { should have_many(:campaigns).dependent(:nullify) }
  end

  describe 'validations' do
    subject { build(:campaign_collection, tenant: tenant) }

    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:collection_type) }
    it { should validate_presence_of(:vibe_strategy) }
    it { should validate_uniqueness_of(:name).scoped_to(:tenant_id) }
  end

  describe 'enums' do
    it { should define_enum_for(:collection_type).with_values(thematic: 0, seasonal: 1, product_launch: 2, brand_awareness: 3, promotional: 4) }
    it { should define_enum_for(:status).with_values(planning: 0, active: 1, paused: 2, completed: 3, archived: 4) }
  end

  describe 'scopes' do
    let!(:active_collection) { create(:campaign_collection, status: 'active', tenant: tenant) }
    let!(:completed_collection) { create(:campaign_collection, status: 'completed', tenant: tenant) }
    let!(:vibe_strategy_collection) { create(:campaign_collection, :with_vibe_strategy, tenant: tenant) }
    let!(:recent_collection) { create(:campaign_collection, tenant: tenant, created_at: 1.day.ago) }
    let!(:old_collection) { create(:campaign_collection, tenant: tenant, created_at: 2.weeks.ago) }

    describe '.active' do
      it 'returns active collections' do
        expect(CampaignCollection.active).to include(active_collection)
        expect(CampaignCollection.active).not_to include(completed_collection)
      end
    end

    describe '.completed' do
      it 'returns completed collections' do
        expect(CampaignCollection.completed).to include(completed_collection)
        expect(CampaignCollection.completed).not_to include(active_collection)
      end
    end

    describe '.by_collection_type' do
      let!(:thematic_collection) { create(:campaign_collection, collection_type: 'thematic', tenant: tenant) }
      
      it 'returns collections of specified type' do
        thematic_collections = CampaignCollection.by_collection_type('thematic')
        expect(thematic_collections).to include(thematic_collection)
      end
    end

    describe '.recent' do
      it 'returns collections from the last week' do
        expect(CampaignCollection.recent).to include(recent_collection)
        expect(CampaignCollection.recent).not_to include(old_collection)
      end
    end

    describe '.with_vibe_strategy' do
      it 'returns collections with comprehensive vibe strategies' do
        expect(CampaignCollection.with_vibe_strategy('comprehensive')).to include(vibe_strategy_collection)
      end
    end
  end

  describe 'instance methods' do
    subject { create(:campaign_collection, tenant: tenant) }

    describe '#strategy_overview' do
      it 'provides vibe strategy overview' do
        collection = create(:campaign_collection,
          tenant: tenant,
          vibe_strategy: {
            'approach' => 'comprehensive',
            'cultural_focus' => ['trending_moments', 'seasonal_relevance'],
            'authenticity_level' => 'high',
            'emotional_targets' => ['joy', 'excitement', 'trust']
          }
        )

        overview = collection.strategy_overview
        expect(overview).to be_a(Hash)
        expect(overview['approach']).to eq('comprehensive')
        expect(overview['cultural_focus']).to be_an(Array)
        expect(overview['emotional_targets']).to include('joy')
      end
    end

    describe '#cultural_coherence' do
      it 'evaluates cultural coherence across campaigns' do
        coherence = subject.cultural_coherence
        expect(coherence).to be_a(Hash)
        expect(coherence).to have_key('coherence_score')
        expect(coherence).to have_key('alignment_factors')
        expect(coherence).to have_key('divergence_points')
      end
    end

    describe '#authenticity_consistency' do
      it 'measures authenticity consistency' do
        consistency = subject.authenticity_consistency
        expect(consistency).to be_a(Hash)
        expect(consistency).to have_key('consistency_score')
        expect(consistency).to have_key('brand_alignment')
        expect(consistency).to have_key('message_coherence')
      end
    end

    describe '#emotional_harmony' do
      it 'analyzes emotional harmony across campaigns' do
        harmony = subject.emotional_harmony
        expect(harmony).to be_a(Hash)
        expect(harmony).to have_key('harmony_score')
        expect(harmony).to have_key('dominant_emotions')
        expect(harmony).to have_key('emotional_journey')
      end
    end

    describe '#collection_performance' do
      it 'aggregates collection performance metrics' do
        performance = subject.collection_performance
        expect(performance).to be_a(Hash)
        expect(performance).to have_key('overall_vibe_score')
        expect(performance).to have_key('campaign_count')
        expect(performance).to have_key('average_cultural_relevance')
        expect(performance).to have_key('authenticity_rating')
      end
    end

    describe '#optimization_opportunities' do
      it 'identifies optimization opportunities' do
        opportunities = subject.optimization_opportunities
        expect(opportunities).to be_an(Array)
        opportunities.each do |opp|
          expect(opp).to have_key('area')
          expect(opp).to have_key('recommendation')
          expect(opp).to have_key('impact_potential')
        end
      end
    end

    describe '#strategic_alignment' do
      it 'evaluates strategic alignment' do
        alignment = subject.strategic_alignment
        expect(alignment).to be_a(Hash)
        expect(alignment).to have_key('alignment_score')
        expect(alignment).to have_key('strategy_gaps')
        expect(alignment).to have_key('reinforcement_areas')
      end
    end

    describe '#campaign_orchestration_health' do
      it 'evaluates orchestration health across campaigns' do
        health = subject.campaign_orchestration_health
        expect(health).to be_a(Hash)
        expect(health).to have_key('orchestration_score')
        expect(health).to have_key('coordination_effectiveness')
        expect(health).to have_key('timing_optimization')
      end
    end

    describe '#add_campaign' do
      let(:campaign) { create(:campaign, tenant: tenant) }

      it 'adds campaign to collection' do
        expect { subject.add_campaign(campaign) }.to change { subject.campaigns.count }.by(1)
        expect(subject.campaigns).to include(campaign)
      end

      it 'updates campaign collection reference' do
        subject.add_campaign(campaign)
        expect(campaign.reload.campaign_collection).to eq(subject)
      end
    end

    describe '#remove_campaign' do
      let(:campaign) { create(:campaign, campaign_collection: subject, tenant: tenant) }

      it 'removes campaign from collection' do
        expect { subject.remove_campaign(campaign) }.to change { subject.campaigns.count }.by(-1)
        expect(subject.campaigns).not_to include(campaign)
      end

      it 'nullifies campaign collection reference' do
        subject.remove_campaign(campaign)
        expect(campaign.reload.campaign_collection).to be_nil
      end
    end

    describe '#vibe_strategy_effectiveness' do
      it 'measures vibe strategy effectiveness' do
        effectiveness = subject.vibe_strategy_effectiveness
        expect(effectiveness).to be_a(Float)
        expect(effectiveness).to be_between(0, 10)
      end
    end

    describe '#requires_strategy_adjustment?' do
      context 'with low performance scores' do
        before do
          allow(subject).to receive(:vibe_strategy_effectiveness).and_return(4.0)
        end

        it 'returns true' do
          expect(subject.requires_strategy_adjustment?).to be true
        end
      end

      context 'with high performance scores' do
        before do
          allow(subject).to receive(:vibe_strategy_effectiveness).and_return(8.5)
        end

        it 'returns false' do
          expect(subject.requires_strategy_adjustment?).to be false
        end
      end
    end

    describe '#activate!' do
      subject { create(:campaign_collection, status: 'planning', tenant: tenant) }
      
      it 'changes status to active' do
        expect { subject.activate! }.to change { subject.status }.from('planning').to('active')
      end

      it 'updates activated_at timestamp' do
        expect { subject.activate! }.to change { subject.activated_at }.from(nil)
      end
    end

    describe '#complete!' do
      subject { create(:campaign_collection, status: 'active', tenant: tenant) }
      
      it 'changes status to completed' do
        expect { subject.complete! }.to change { subject.status }.from('active').to('completed')
      end

      it 'updates completed_at timestamp' do
        expect { subject.complete! }.to change { subject.completed_at }.from(nil)
      end
    end

    describe '#archive!' do
      subject { create(:campaign_collection, status: 'completed', tenant: tenant) }
      
      it 'changes status to archived' do
        expect { subject.archive! }.to change { subject.status }.from('completed').to('archived')
      end

      it 'updates archived_at timestamp' do
        expect { subject.archive! }.to change { subject.archived_at }.from(nil)
      end
    end
  end

  describe 'JSON attribute handling' do
    it 'properly handles vibe_strategy as JSON' do
      strategy = {
        'approach' => 'comprehensive',
        'cultural_focus' => ['trending_moments', 'seasonal_relevance'],
        'authenticity_level' => 'high',
        'emotional_targets' => ['joy', 'excitement', 'trust'],
        'timing_strategy' => 'real_time_adaptation'
      }
      collection = create(:campaign_collection, vibe_strategy: strategy, tenant: tenant)
      
      expect(collection.vibe_strategy).to eq(strategy)
    end

    it 'properly handles metadata as JSON' do
      metadata = {
        'target_demographics' => ['millennials', 'gen_z'],
        'geographic_focus' => ['north_america', 'europe'],
        'budget_allocation' => { 'content_creation' => 40, 'media_spend' => 60 }
      }
      collection = create(:campaign_collection, metadata: metadata, tenant: tenant)
      
      expect(collection.metadata).to eq(metadata)
    end
  end

  describe 'callbacks' do
    it 'sets default status to planning on creation' do
      collection = create(:campaign_collection, tenant: tenant)
      expect(collection.status).to eq('planning')
    end

    it 'updates timestamp on status change' do
      collection = create(:campaign_collection, tenant: tenant)
      original_updated_at = collection.updated_at
      
      sleep(0.1) # Ensure time difference
      collection.activate!
      
      expect(collection.updated_at).to be > original_updated_at
    end
  end

  describe 'factory validity' do
    it 'creates a valid campaign collection' do
      collection = build(:campaign_collection, tenant: tenant)
      expect(collection).to be_valid
    end

    it 'creates valid collection with vibe strategy' do
      collection = build(:campaign_collection, :with_vibe_strategy, tenant: tenant)
      expect(collection).to be_valid
      expect(collection.vibe_strategy['approach']).to eq('comprehensive')
      expect(collection.name).to eq('Comprehensive Vibe Strategy Collection')
    end

    it 'creates valid seasonal collection' do
      collection = build(:campaign_collection, :seasonal, tenant: tenant)
      expect(collection).to be_valid
      expect(collection.collection_type).to eq('seasonal')
      expect(collection.name).to include('Seasonal')
    end

    it 'creates valid product launch collection' do
      collection = build(:campaign_collection, :product_launch, tenant: tenant)
      expect(collection).to be_valid
      expect(collection.collection_type).to eq('product_launch')
      expect(collection.name).to include('Product Launch')
    end

    it 'creates valid brand awareness collection' do
      collection = build(:campaign_collection, :brand_awareness, tenant: tenant)
      expect(collection).to be_valid
      expect(collection.collection_type).to eq('brand_awareness')
      expect(collection.name).to include('Brand Awareness')
    end
  end
end
