# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VibeAnalysisRecord, type: :model do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe 'associations' do
    it { should belong_to(:campaign).required(true) }
    
    it 'belongs to tenant' do
      expect(subject.class.reflect_on_association(:tenant)).to be_present
      expect(subject.class.reflect_on_association(:tenant).macro).to eq(:belongs_to)
    end
  end

  describe 'validations' do
    subject { build(:vibe_analysis_record, campaign: campaign, tenant: tenant) }

    it { should validate_presence_of(:analysis_type) }
    it { should validate_numericality_of(:confidence_score).is_greater_than_or_equal_to(0.0).is_less_than_or_equal_to(100.0) }
    
    it 'validates analysis_type inclusion' do
      expect(subject).to be_valid
      
      # Test that valid values work
      %w[emotional cultural authenticity psychographic].each do |valid_type|
        subject.analysis_type = valid_type
        expect(subject).to be_valid
      end
      
      # Test that invalid values are rejected through enum
      expect {
        subject.analysis_type = 'invalid_type'
      }.to raise_error(ArgumentError, /'invalid_type' is not a valid analysis_type/)
    end
    
    it 'validates uniqueness of analysis_type scoped to campaign' do
      subject.save!
      duplicate = build(:vibe_analysis_record, campaign: campaign, tenant: tenant, analysis_type: subject.analysis_type)
      expect(duplicate).not_to be_valid
      expect(duplicate.errors[:analysis_type]).to include('has already been taken')
    end
  end

  describe 'enums' do
    it 'defines analysis_type enum' do
      expect(VibeAnalysisRecord.analysis_types.keys).to match_array(%w[emotional cultural authenticity psychographic])
    end
  end

  describe 'scopes' do
    let!(:high_confidence_record) { create(:vibe_analysis_record, :high_performing, campaign: campaign, tenant: tenant) }
    let!(:low_confidence_record) { create(:vibe_analysis_record, :low_confidence, campaign: campaign, tenant: tenant, analysis_type: 'cultural') }
    let!(:recent_record) { create(:vibe_analysis_record, campaign: campaign, tenant: tenant, analysis_type: 'authenticity', created_at: 1.hour.ago) }
    let!(:old_record) { create(:vibe_analysis_record, campaign: campaign, tenant: tenant, analysis_type: 'psychographic', created_at: 2.days.ago) }

    describe '.high_confidence' do
      it 'returns records with confidence score >= 80.0' do
        expect(VibeAnalysisRecord.high_confidence).to include(high_confidence_record)
        expect(VibeAnalysisRecord.high_confidence).not_to include(low_confidence_record)
      end
    end

    describe '.recent' do
      it 'returns records ordered by created_at desc' do
        records = VibeAnalysisRecord.recent
        expect(records.first.created_at).to be >= records.last.created_at
      end
    end
  end

  describe 'instance methods' do
    subject { create(:vibe_analysis_record, campaign: campaign, tenant: tenant) }

    describe '#summary' do
      it 'returns a summary of the analysis' do
        summary = subject.summary
        
        expect(summary).to be_a(String)
        expect(summary).not_to be_empty
      end
    end

    describe '#high_confidence?' do
      context 'with high confidence score' do
        subject { create(:vibe_analysis_record, :high_performing, campaign: campaign, tenant: tenant) }
        
        it 'returns true' do
          expect(subject.high_confidence?).to be true
        end
      end

      context 'with low confidence score' do
        subject { create(:vibe_analysis_record, :low_confidence, campaign: campaign, tenant: tenant) }
        
        it 'returns false' do
          expect(subject.high_confidence?).to be false
        end
      end
    end

    describe '#primary_emotion' do
      context 'for emotional analysis' do
        subject { create(:vibe_analysis_record, :emotional, campaign: campaign, tenant: tenant) }
        
        it 'returns the primary emotion' do
          expect(subject.primary_emotion).to eq('joy')
        end
      end

      context 'for non-emotional analysis' do
        subject { create(:vibe_analysis_record, :cultural, campaign: campaign, tenant: tenant) }
        
        it 'returns nil' do
          expect(subject.primary_emotion).to be_nil
        end
      end
    end
  end

  describe 'factory validity' do
    it 'creates a valid vibe analysis record' do
      record = build(:vibe_analysis_record, campaign: campaign, tenant: tenant)
      expect(record).to be_valid
    end

    it 'creates valid cultural analysis record' do
      record = build(:vibe_analysis_record, :cultural, campaign: campaign, tenant: tenant)
      expect(record).to be_valid
      expect(record.analysis_type).to eq('cultural')
    end

    it 'creates valid authenticity analysis record' do
      record = build(:vibe_analysis_record, :authenticity, campaign: campaign, tenant: tenant)
      expect(record).to be_valid
      expect(record.analysis_type).to eq('authenticity')
    end

    it 'creates valid psychographic analysis record' do
      record = build(:vibe_analysis_record, :psychographic, campaign: campaign, tenant: tenant)
      expect(record).to be_valid
      expect(record.analysis_type).to eq('psychographic')
    end
  end
end
