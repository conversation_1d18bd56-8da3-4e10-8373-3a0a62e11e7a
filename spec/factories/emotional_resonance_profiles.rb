# frozen_string_literal: true

FactoryBot.define do
  factory :emotional_resonance_profile do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :campaign
    
    profile_name { "#{Faker::Emotion.adjective.capitalize} #{Faker::Marketing.buzzwords} Profile" }
    primary_emotion { 'joy' }
    secondary_emotions { ['trust', 'anticipation'] }
    emotional_intensity { 'moderate' }
    target_demographics { ['millennials', 'gen_z'] }
    seasonal_relevance { 'high' }
    
    resonance_data do
      {
        plutchik_wheel: {
          primary: 'joy',
          secondary: ['trust', 'anticipation'],
          combinations: ['optimism', 'love'],
          intensity_level: 0.75
        },
        emotional_journey: {
          awareness_stage: 'curiosity',
          consideration_stage: 'interest',
          decision_stage: 'trust',
          retention_stage: 'joy'
        },
        demographic_alignment: {
          age_groups: {
            '18-24': 8.5,
            '25-34': 9.2,
            '35-44': 7.8
          },
          cultural_contexts: {
            'north_america': 8.7,
            'europe': 8.3,
            'asia': 7.9
          }
        }
      }
    end
    
    performance_metrics do
      {
        engagement_lift: 23.5,
        conversion_improvement: 15.2,
        brand_affinity_score: 8.4,
        emotional_recall: 78.3,
        sentiment_improvement: 12.8
      }
    end

    trait :high_intensity do
      emotional_intensity { 'intense' }
      resonance_data do
        {
          plutchik_wheel: {
            primary: 'joy',
            secondary: ['trust', 'anticipation'],
            combinations: ['optimism', 'love'],
            intensity_level: 0.95
          }
        }
      end
    end
  end
end
