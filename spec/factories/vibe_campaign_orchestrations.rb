# frozen_string_literal: true

FactoryBot.define do
  factory :vibe_campaign_orchestration do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :campaign_collection
    
    orchestration_name { "#{Faker::Marketing.buzzwords} Orchestration Strategy" }
    vibe_strategy_type { 'emotional_journey_mapping' }
    orchestration_status { 'active' }
    priority_level { 'medium' }
    target_vibe_score { 8.5 }
    current_vibe_score { 7.8 }
    optimization_count { 2 }
    started_at { 1.week.ago }
    last_optimized_at { 2.days.ago }
    
    orchestration_data do
      {
        config: {
          emotional_journey: {
            stages: ['awareness', 'consideration', 'decision', 'retention'],
            transitions: {
              'awareness_to_consideration': { emotion: 'curiosity', timing: '2_days' },
              'consideration_to_decision': { emotion: 'trust', timing: '5_days' },
              'decision_to_retention': { emotion: 'satisfaction', timing: '1_week' }
            }
          },
          optimization_rules: {
            min_vibe_threshold: 7.0,
            auto_optimize: true,
            optimization_frequency: 'daily'
          }
        },
        metrics: {
          campaign_alignment_score: 8.2,
          execution_efficiency: 0.87,
          audience_engagement: 0.74
        }
      }
    end

    trait :cultural_surfing do
      vibe_strategy_type { 'cultural_moment_surfing' }
      orchestration_data do
        {
          config: {
            cultural_moments: ['Sustainability Week', 'Mental Health Awareness'],
            timing_strategy: 'peak_engagement',
            cultural_sensitivity_level: 'high'
          }
        }
      end
    end

    trait :authenticity_focused do
      vibe_strategy_type { 'authenticity_amplification' }
      target_vibe_score { 9.0 }
      orchestration_data do
        {
          config: {
            authenticity_dimensions: ['transparency', 'consistency', 'credibility'],
            validation_frequency: 'real_time',
            risk_tolerance: 'low'
          }
        }
      end
    end

    trait :high_performing do
      current_vibe_score { 9.2 }
      optimization_count { 0 }
      orchestration_status { 'completed' }
    end

    trait :needs_optimization do
      current_vibe_score { 6.1 }
      target_vibe_score { 8.5 }
      optimization_count { 5 }
      orchestration_status { 'optimizing' }
    end
  end
end
