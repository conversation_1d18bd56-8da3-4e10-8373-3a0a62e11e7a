# frozen_string_literal: true

FactoryBot.define do
  factory :cultural_moment do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    
    sequence(:title) { |n| "#{['Sustainability Week', 'Mental Health Awareness', 'Digital Wellness Month', 'Innovation Summit'][n % 4]} #{n}" }
    sequence(:description) { |n| "A significant cultural moment focused on #{['environmental consciousness', 'mental wellbeing', 'digital balance', 'technological advancement'][n % 4]}" }
    category { 'social_awareness' }
    start_date { 1.week.from_now }
    end_date { 3.weeks.from_now }
    relevance_score { rand(7.0..9.5).round(2) }
    
    engagement_metrics do
      {
        social_mentions: rand(1000..10000),
        hashtag_usage: rand(500..5000),
        media_coverage: rand(50..200),
        sentiment_score: rand(7.0..9.0).round(2),
        viral_potential: rand(6.0..9.5).round(2)
      }
    end
    
    cultural_data do
      {
        trending_topics: ['sustainability', 'awareness', 'community'],
        demographics: {
          primary_age_groups: ['25-34', '35-44'],
          geographic_focus: ['north_america', 'europe'],
          interests: ['environment', 'health', 'technology']
        },
        timing_factors: {
          peak_engagement_hours: ['10:00', '14:00', '19:00'],
          optimal_days: ['tuesday', 'wednesday', 'thursday'],
          seasonal_relevance: 'high'
        }
      }
    end

    trait :trending do
      relevance_score { rand(8.5..10.0).round(2) }
      engagement_metrics do
        {
          social_mentions: rand(8000..15000),
          hashtag_usage: rand(4000..8000),
          media_coverage: rand(150..300),
          sentiment_score: rand(8.5..9.5).round(2),
          viral_potential: rand(8.5..10.0).round(2)
        }
      end
    end

    trait :expired do
      start_date { 3.weeks.ago }
      end_date { 1.week.ago }
      relevance_score { rand(3.0..5.5).round(2) }
    end

    trait :future do
      start_date { 4.weeks.from_now }
      end_date { 6.weeks.from_now }
    end

    trait :technology_focused do
      category { 'technology' }
      title { 'AI Innovation Week' }
      description { 'A week celebrating artificial intelligence and machine learning breakthroughs' }
      cultural_data do
        {
          trending_topics: ['AI', 'machine_learning', 'innovation'],
          demographics: {
            primary_age_groups: ['25-34', '35-44'],
            geographic_focus: ['silicon_valley', 'tech_hubs'],
            interests: ['technology', 'innovation', 'future']
          }
        }
      end
    end
  end
end