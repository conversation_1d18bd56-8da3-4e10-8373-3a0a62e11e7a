# frozen_string_literal: true

FactoryBot.define do
  factory :campaign_collection do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :created_by, factory: :user
    
    sequence(:name) { |n| "#{Faker::Marketing.buzzwords} Collection #{n}" }
    description { Faker::Lorem.paragraph(sentence_count: 2) }
    collection_type { 'sequence' }
    status { 'draft' }
    
    settings do
      {
        automation: {
          auto_sequence: true,
          trigger_delays: ['2_days', '5_days', '1_week']
        },
        targeting: {
          shared_audience: true,
          audience_overlap_strategy: 'progressive'
        }
      }
    end

    # Vibe Marketing traits
    trait :with_vibe_strategy do
      vibe_strategy do
        {
          emotional_journey: {
            progression: ['curiosity', 'interest', 'trust', 'satisfaction'],
            intensity_curve: 'gradual_increase'
          },
          cultural_alignment: {
            target_moments: ['Sustainability Week'],
            sensitivity_level: 'high'
          },
          authenticity_focus: {
            dimensions: ['transparency', 'consistency'],
            validation_level: 'comprehensive'
          }
        }
      end
      target_emotion { 'joy' }
    end

    trait :cultural_moment_focused do
      cultural_moment { 'Sustainability Week' }
      vibe_strategy do
        {
          cultural_moment_strategy: {
            timing: 'peak_engagement',
            message_alignment: 'high',
            trend_capitalization: true
          }
        }
      end
    end

    trait :emotional_journey do
      target_emotion { 'trust' }
      vibe_strategy do
        {
          emotional_journey: {
            start_emotion: 'curiosity',
            peak_emotion: 'excitement',
            end_emotion: 'trust',
            journey_length: '2_weeks'
          }
        }
      end
    end

    trait :with_orchestration do
      after(:create) do |collection|
        create(:vibe_campaign_orchestration, campaign_collection: collection)
      end
    end

    trait :active do
      status { 'active' }
    end

    trait :multi_channel do
      collection_type { 'multi_channel' }
    end
  end
end
