# frozen_string_literal: true

FactoryBot.define do
  factory :campaign do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :created_by, factory: :user
    
    sequence(:name) { |n| "#{Faker::Marketing.buzzwords} Campaign #{n}" }
    description { Faker::Lorem.paragraph(sentence_count: 3) }
    campaign_type { 'email' }
    status { 'draft' }
    target_audience { Faker::Company.industry }
    
    start_date { 1.week.from_now }
    end_date { 3.weeks.from_now }
    
    budget_cents { rand(10000..100000) } # $100 to $1000 in cents
    
    settings do
      {
        goals: {
          primary: 'increase_engagement',
          metrics: ['open_rate', 'click_rate', 'conversion_rate']
        },
        targeting: {
          demographics: ['small_business_owners'],
          interests: ['marketing', 'automation'],
          geographic: ['north_america']
        }
      }
    end

    trait :active do
      status { 'active' }
      start_date { 1.day.ago }
    end

    trait :completed do
      status { 'completed' }
      start_date { 2.weeks.ago }
      end_date { 1.week.ago }
    end

    trait :multi_channel do
      campaign_type { 'multi_channel' }
    end

    trait :social do
      campaign_type { 'social' }
    end

    trait :seo do
      campaign_type { 'seo' }
    end

    trait :with_large_budget do
      budget_cents { rand(500000..1000000) } # $5000 to $10000
    end
  end
end
