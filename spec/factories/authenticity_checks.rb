# frozen_string_literal: true

FactoryBot.define do
  factory :authenticity_check do
    # Use current tenant if available, otherwise create one
    tenant { ActsAsTenant.current_tenant || association(:tenant) }
    association :campaign
    
    check_type { 'brand_voice_consistency' }
    status { 'approved' }
    authenticity_score { 8.5 }
    
    findings do
      {
        transparency: {
          score: 8.5,
          notes: 'Clear and honest messaging with transparent value proposition'
        },
        consistency: {
          score: 8.7,
          notes: 'Maintains consistent brand voice across all touchpoints'
        },
        credibility: {
          score: 8.9,
          notes: 'Strong credibility indicators and trustworthy messaging'
        },
        genuineness: {
          score: 8.1,
          notes: 'Authentic tone that feels genuine and not overly promotional'
        },
        relatability: {
          score: 7.8,
          notes: 'Good connection with target audience, could be more relatable'
        },
        cultural_awareness: {
          score: 9.1,
          notes: 'Excellent cultural sensitivity and awareness'
        },
        ethical_alignment: {
          score: 8.6,
          notes: 'Strong ethical positioning aligned with brand values'
        },
        emotional_honesty: {
          score: 8.2,
          notes: 'Emotionally honest without manipulation'
        }
      }
    end
    
    recommendations do
      [
        'Consider adding more personal stories to increase relatability',
        'Maintain current level of cultural sensitivity',
        'Continue authentic voice - it resonates well with audience'
      ]
    end
    
    # Store risk assessment data in the findings jsonb field instead of using a separate attribute
    after(:build) do |check|
      risk_data = {
        overall_risk: 'low',
        cultural_risks: [],
        brand_risks: ['Minor inconsistency in secondary messaging'],
        compliance_risks: []
      }
      
      # Store within findings for access through risk_assessment method
      check.findings['risk_assessment'] = risk_data
    end

    trait :flagged do
      status { 'flagged' }
      authenticity_score { 5.8 }
      findings do
        {
          transparency: { score: 6.2, notes: 'Some messaging lacks clarity' },
          cultural_awareness: { score: 4.5, notes: 'Potential cultural sensitivity issues detected' }
        }
      end
      after(:build) do |check|
        risk_data = {
          overall_risk: 'medium',
          cultural_risks: ['Potential misinterpretation in certain cultural contexts'],
          brand_risks: ['Inconsistent messaging tone'],
          compliance_risks: []
        }
        check.findings['risk_assessment'] = risk_data
      end
    end

    trait :rejected do
      status { 'rejected' }
      authenticity_score { 3.2 }
      after(:build) do |check|
        risk_data = {
          overall_risk: 'high',
          cultural_risks: ['Significant cultural sensitivity concerns'],
          brand_risks: ['Major brand voice inconsistency'],
          compliance_risks: ['Potential regulatory compliance issues']
        }
        check.findings['risk_assessment'] = risk_data
      end
    end
  end
end
