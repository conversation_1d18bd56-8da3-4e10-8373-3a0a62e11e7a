# frozen_string_literal: true

require 'rails_helper'

RSpec.describe 'Campaign Validation Integration', type: :request do
  let(:tenant) { create(:tenant) }
  let(:user) { create(:user, tenant: tenant) }

  before do
    ActsAsTenant.current_tenant = tenant
    # Skip authentication for now to focus on validation logic
    allow_any_instance_of(ApplicationController).to receive(:authenticate_user!).and_return(true)
    allow_any_instance_of(ApplicationController).to receive(:current_user).and_return(user)
  end

  describe 'POST /campaigns with validation contract' do
    context 'with valid parameters' do
      let(:valid_params) do
        {
          campaign: {
            name: 'Test Campaign',
            campaign_type: 'email',
            target_audience: 'Young professionals aged 25-35',
            budget_in_dollars: 500.00,
            start_date: Date.current + 1.day,
            end_date: Date.current + 30.days,
            description: 'A test campaign for validation'
          }
        }
      end

      it 'creates a new campaign' do
        expect {
          post '/campaigns', params: valid_params
        }.to change(Campaign, :count).by(1)
      end

      it 'redirects to the campaign' do
        post '/campaigns', params: valid_params
        expect(response).to redirect_to(Campaign.last)
      end

      it 'sets success notice' do
        post '/campaigns', params: valid_params
        expect(flash[:notice]).to eq('Campaign was successfully created.')
      end
    end

    context 'with invalid parameters' do
      let(:invalid_params) do
        {
          campaign: {
            name: '', # Invalid: empty name
            campaign_type: 'invalid_type', # Invalid: not in allowed types
            target_audience: 'ab', # Invalid: too short
            budget_in_dollars: -100, # Invalid: negative budget
            start_date: Date.current - 1.day, # Invalid: in the past
            end_date: Date.current + 1.day
          }
        }
      end

      it 'does not create a campaign' do
        expect {
          post '/campaigns', params: invalid_params
        }.not_to change(Campaign, :count)
      end

      it 'renders the new template' do
        post '/campaigns', params: invalid_params
        expect(response).to render_template(:new)
      end

      it 'returns unprocessable entity status' do
        post '/campaigns', params: invalid_params
        expect(response).to have_http_status(:unprocessable_entity)
      end

      it 'includes validation errors in flash' do
        post '/campaigns', params: invalid_params
        expect(flash[:alert]).to be_present
        expect(flash[:alert]).to include('validation')
      end
    end

    context 'with date validation errors' do
      let(:invalid_date_params) do
        {
          campaign: {
            name: 'Test Campaign',
            campaign_type: 'email',
            target_audience: 'Young professionals aged 25-35',
            budget_in_dollars: 500.00,
            start_date: Date.current + 30.days,
            end_date: Date.current + 1.day # End date before start date
          }
        }
      end

      it 'does not create a campaign' do
        expect {
          post '/campaigns', params: invalid_date_params
        }.not_to change(Campaign, :count)
      end

      it 'includes date validation error' do
        post '/campaigns', params: invalid_date_params
        expect(flash[:alert]).to include('end date must be after start date')
      end
    end
  end

  describe 'PATCH /campaigns/:id with validation contract' do
    let(:campaign) { create(:campaign, tenant: tenant, created_by: user) }

    context 'with valid parameters' do
      let(:valid_update_params) do
        {
          id: campaign.id,
          campaign: {
            name: 'Updated Campaign Name',
            description: 'Updated description'
          }
        }
      end

      it 'updates the campaign' do
        patch "/campaigns/#{campaign.id}", params: valid_update_params
        campaign.reload
        expect(campaign.name).to eq('Updated Campaign Name')
        expect(campaign.description).to eq('Updated description')
      end

      it 'redirects to the campaign' do
        patch "/campaigns/#{campaign.id}", params: valid_update_params
        expect(response).to redirect_to(campaign)
      end
    end

    context 'with invalid parameters' do
      let(:invalid_update_params) do
        {
          id: campaign.id,
          campaign: {
            name: '', # Invalid: empty name
            campaign_type: 'invalid_type' # Invalid: not in allowed types
          }
        }
      end

      it 'does not update the campaign' do
        original_name = campaign.name
        patch "/campaigns/#{campaign.id}", params: invalid_update_params
        campaign.reload
        expect(campaign.name).to eq(original_name)
      end

      it 'renders the edit template' do
        patch "/campaigns/#{campaign.id}", params: invalid_update_params
        expect(response).to render_template(:edit)
      end

      it 'includes validation errors' do
        patch "/campaigns/#{campaign.id}", params: invalid_update_params
        expect(flash[:alert]).to be_present
      end
    end
  end
end
