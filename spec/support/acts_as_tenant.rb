# spec/support/acts_as_tenant.rb
RSpec.configure do |config|
  config.before(:each) do
    # Set a default tenant for tests that require one
    if described_class.respond_to?(:acts_as_tenant) || 
       (described_class.respond_to?(:reflect_on_association) && 
        described_class.reflect_on_association(:tenant))
      
      @default_tenant = create(:tenant)
      ActsAsTenant.current_tenant = @default_tenant
    end
  end

  config.after(:each) do
    # Clear the tenant after each test
    ActsAsTenant.current_tenant = nil
  end
end
