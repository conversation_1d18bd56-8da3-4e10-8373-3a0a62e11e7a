# frozen_string_literal: true

require 'rails_helper'

RSpec.describe AuthenticityCheckerTool, type: :service do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }
  let(:tool) { described_class.new(campaign) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#initialize' do
    it 'initializes with a campaign' do
      expect(tool.campaign).to eq(campaign)
    end

    it 'raises error without campaign' do
      expect { described_class.new(nil) }.to raise_error(ArgumentError, 'Campaign is required')
    end
  end

  describe '#perform_authenticity_check' do
    it 'performs comprehensive authenticity analysis' do
      result = tool.perform_authenticity_check
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:overall_score)
      expect(result).to have_key(:dimension_scores)
      expect(result).to have_key(:flagged_elements)
      expect(result).to have_key(:recommendations)
      expect(result).to have_key(:authenticity_check_record)
    end

    it 'creates authenticity check record' do
      expect { tool.perform_authenticity_check }.to change { AuthenticityCheck.count }.by(1)
    end

    it 'associates check with campaign and tenant' do
      result = tool.perform_authenticity_check
      check_record = result[:authenticity_check_record]
      
      expect(check_record.campaign).to eq(campaign)
      expect(check_record.tenant).to eq(tenant)
    end

    it 'calculates overall authenticity score' do
      result = tool.perform_authenticity_check
      score = result[:overall_score]
      
      expect(score).to be_a(Float)
      expect(score).to be_between(0, 10)
    end
  end

  describe '#brand_consistency_check' do
    it 'evaluates brand consistency' do
      result = tool.brand_consistency_check
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:consistency_score)
      expect(result).to have_key(:brand_alignment_factors)
      expect(result).to have_key(:deviation_points)
      expect(result).to have_key(:improvement_suggestions)
    end

    it 'identifies brand alignment factors' do
      result = tool.brand_consistency_check
      factors = result[:brand_alignment_factors]
      
      expect(factors).to be_a(Hash)
      expect(factors).to have_key(:visual_consistency)
      expect(factors).to have_key(:tone_alignment)
      expect(factors).to have_key(:messaging_coherence)
      expect(factors).to have_key(:value_alignment)
    end

    it 'flags deviation points' do
      result = tool.brand_consistency_check
      deviations = result[:deviation_points]
      
      expect(deviations).to be_an(Array)
      deviations.each do |deviation|
        expect(deviation).to have_key(:area)
        expect(deviation).to have_key(:severity)
        expect(deviation).to have_key(:description)
      end
    end
  end

  describe '#cultural_sensitivity_analysis' do
    it 'analyzes cultural sensitivity' do
      result = tool.cultural_sensitivity_analysis
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:sensitivity_score)
      expect(result).to have_key(:cultural_considerations)
      expect(result).to have_key(:potential_issues)
      expect(result).to have_key(:recommendations)
    end

    it 'identifies cultural considerations' do
      result = tool.cultural_sensitivity_analysis
      considerations = result[:cultural_considerations]
      
      expect(considerations).to be_a(Hash)
      expect(considerations).to have_key(:target_cultures)
      expect(considerations).to have_key(:cultural_context)
      expect(considerations).to have_key(:sensitivity_factors)
    end

    it 'flags potential cultural issues' do
      result = tool.cultural_sensitivity_analysis
      issues = result[:potential_issues]
      
      expect(issues).to be_an(Array)
      issues.each do |issue|
        expect(issue).to have_key(:issue_type)
        expect(issue).to have_key(:severity_level)
        expect(issue).to have_key(:affected_groups)
        expect(issue).to have_key(:mitigation_suggestions)
      end
    end
  end

  describe '#emotional_genuineness_evaluation' do
    it 'evaluates emotional genuineness' do
      result = tool.emotional_genuineness_evaluation
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:genuineness_score)
      expect(result).to have_key(:authenticity_indicators)
      expect(result).to have_key(:artificial_elements)
      expect(result).to have_key(:enhancement_suggestions)
    end

    it 'identifies authenticity indicators' do
      result = tool.emotional_genuineness_evaluation
      indicators = result[:authenticity_indicators]
      
      expect(indicators).to be_a(Hash)
      expect(indicators).to have_key(:emotional_depth)
      expect(indicators).to have_key(:natural_expression)
      expect(indicators).to have_key(:relatable_elements)
    end

    it 'flags artificial elements' do
      result = tool.emotional_genuineness_evaluation
      artificial = result[:artificial_elements]
      
      expect(artificial).to be_an(Array)
      artificial.each do |element|
        expect(element).to have_key(:element_type)
        expect(element).to have_key(:artificiality_level)
        expect(element).to have_key(:impact_assessment)
      end
    end
  end

  describe '#claim_accuracy_verification' do
    it 'verifies claim accuracy' do
      result = tool.claim_accuracy_verification
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:accuracy_score)
      expect(result).to have_key(:verified_claims)
      expect(result).to have_key(:questionable_claims)
      expect(result).to have_key(:supporting_evidence)
    end

    it 'categorizes claims by verification status' do
      result = tool.claim_accuracy_verification
      verified = result[:verified_claims]
      questionable = result[:questionable_claims]
      
      expect(verified).to be_an(Array)
      expect(questionable).to be_an(Array)
      
      (verified + questionable).each do |claim|
        expect(claim).to have_key(:claim_text)
        expect(claim).to have_key(:verification_status)
        expect(claim).to have_key(:confidence_level)
      end
    end

    it 'provides supporting evidence' do
      result = tool.claim_accuracy_verification
      evidence = result[:supporting_evidence]
      
      expect(evidence).to be_a(Hash)
      evidence.each_value do |claim_evidence|
        expect(claim_evidence).to have_key(:sources)
        expect(claim_evidence).to have_key(:credibility_score)
      end
    end
  end

  describe '#compliance_assessment' do
    it 'assesses regulatory compliance' do
      assessment = tool.compliance_assessment
      
      expect(assessment).to be_a(Hash)
      expect(assessment).to have_key(:compliance_score)
      expect(assessment).to have_key(:regulatory_requirements)
      expect(assessment).to have_key(:compliance_gaps)
      expect(assessment).to have_key(:risk_level)
    end

    it 'identifies regulatory requirements' do
      assessment = tool.compliance_assessment
      requirements = assessment[:regulatory_requirements]
      
      expect(requirements).to be_an(Array)
      requirements.each do |req|
        expect(req).to have_key(:regulation_type)
        expect(req).to have_key(:applicability)
        expect(req).to have_key(:compliance_status)
      end
    end

    it 'flags compliance gaps' do
      assessment = tool.compliance_assessment
      gaps = assessment[:compliance_gaps]
      
      expect(gaps).to be_an(Array)
      gaps.each do |gap|
        expect(gap).to have_key(:gap_type)
        expect(gap).to have_key(:severity)
        expect(gap).to have_key(:corrective_actions)
      end
    end
  end

  describe '#authenticity_scoring_algorithm' do
    let(:dimension_scores) do
      {
        brand_consistency: 8.5,
        cultural_sensitivity: 7.2,
        emotional_genuineness: 9.1,
        claim_accuracy: 6.8,
        compliance_status: 8.0
      }
    end

    it 'calculates weighted authenticity score' do
      score = tool.authenticity_scoring_algorithm(dimension_scores)
      
      expect(score).to be_a(Float)
      expect(score).to be_between(0, 10)
    end

    it 'applies appropriate weighting to dimensions' do
      score = tool.authenticity_scoring_algorithm(dimension_scores)
      
      # Score should reflect the weighted average of dimensions
      expected_range = 7.5..8.5
      expect(score).to be_between(expected_range.first, expected_range.last)
    end
  end

  describe '#flagged_content_analysis' do
    let(:flagged_elements) do
      ['unsubstantiated_claim', 'potential_cultural_insensitivity', 'brand_tone_mismatch']
    end

    it 'analyzes flagged content elements' do
      analysis = tool.flagged_content_analysis(flagged_elements)
      
      expect(analysis).to be_a(Hash)
      expect(analysis).to have_key(:severity_assessment)
      expect(analysis).to have_key(:impact_analysis)
      expect(analysis).to have_key(:resolution_priorities)
      expect(analysis).to have_key(:timeline_recommendations)
    end

    it 'assesses severity of flagged elements' do
      analysis = tool.flagged_content_analysis(flagged_elements)
      severity = analysis[:severity_assessment]
      
      expect(severity).to be_a(Hash)
      expect(severity).to have_key(:critical_issues)
      expect(severity).to have_key(:moderate_issues)
      expect(severity).to have_key(:minor_issues)
    end

    it 'prioritizes resolution actions' do
      analysis = tool.flagged_content_analysis(flagged_elements)
      priorities = analysis[:resolution_priorities]
      
      expect(priorities).to be_an(Array)
      priorities.each do |priority|
        expect(priority).to have_key(:element)
        expect(priority).to have_key(:priority_level)
        expect(priority).to have_key(:recommended_action)
      end
    end
  end

  describe '#improvement_recommendations' do
    let(:analysis_results) do
      {
        overall_score: 6.5,
        weak_areas: ['claim_accuracy', 'cultural_sensitivity'],
        flagged_elements: ['unverified_statistic', 'cultural_reference'],
        compliance_gaps: ['disclosure_requirement']
      }
    end

    it 'generates improvement recommendations' do
      recommendations = tool.improvement_recommendations(analysis_results)
      
      expect(recommendations).to be_an(Array)
      recommendations.each do |rec|
        expect(rec).to have_key(:area)
        expect(rec).to have_key(:suggestion)
        expect(rec).to have_key(:priority)
        expect(rec).to have_key(:expected_impact)
        expect(rec).to have_key(:implementation_effort)
      end
    end

    it 'prioritizes recommendations by impact and effort' do
      recommendations = tool.improvement_recommendations(analysis_results)
      
      # High-impact, low-effort recommendations should be prioritized
      high_priority_recs = recommendations.select { |r| r[:priority] == 'high' }
      expect(high_priority_recs).not_to be_empty
    end
  end

  describe '#risk_assessment' do
    let(:authenticity_data) do
      {
        overall_score: 5.5,
        flagged_elements: ['misleading_claim', 'cultural_insensitivity'],
        compliance_gaps: ['ftc_disclosure', 'accessibility_standard'],
        cultural_sensitivity_score: 4.2
      }
    end

    it 'assesses authenticity-related risks' do
      assessment = tool.risk_assessment(authenticity_data)
      
      expect(assessment).to be_a(Hash)
      expect(assessment).to have_key(:overall_risk_level)
      expect(assessment).to have_key(:risk_categories)
      expect(assessment).to have_key(:mitigation_strategies)
      expect(assessment).to have_key(:monitoring_requirements)
    end

    it 'categorizes different types of risks' do
      assessment = tool.risk_assessment(authenticity_data)
      categories = assessment[:risk_categories]
      
      expect(categories).to be_a(Hash)
      expect(categories).to have_key(:legal_risk)
      expect(categories).to have_key(:reputation_risk)
      expect(categories).to have_key(:cultural_risk)
      expect(categories).to have_key(:compliance_risk)
    end

    it 'provides mitigation strategies' do
      assessment = tool.risk_assessment(authenticity_data)
      strategies = assessment[:mitigation_strategies]
      
      expect(strategies).to be_an(Array)
      strategies.each do |strategy|
        expect(strategy).to have_key(:risk_area)
        expect(strategy).to have_key(:mitigation_approach)
        expect(strategy).to have_key(:implementation_timeline)
      end
    end
  end

  describe 'private methods' do
    describe '#extract_campaign_claims' do
      it 'extracts claims from campaign content' do
        claims = tool.send(:extract_campaign_claims)
        
        expect(claims).to be_an(Array)
        claims.each do |claim|
          expect(claim).to have_key(:text)
          expect(claim).to have_key(:claim_type)
          expect(claim).to have_key(:verifiability)
        end
      end
    end

    describe '#analyze_brand_guidelines_alignment' do
      it 'analyzes alignment with brand guidelines' do
        alignment = tool.send(:analyze_brand_guidelines_alignment)
        
        expect(alignment).to be_a(Hash)
        expect(alignment).to have_key(:visual_alignment)
        expect(alignment).to have_key(:messaging_alignment)
        expect(alignment).to have_key(:tone_alignment)
      end
    end

    describe '#detect_cultural_sensitivities' do
      let(:content) { { text: ['Welcome everyone!', 'Join our community'], visuals: ['diverse_group.jpg'] } }

      it 'detects potential cultural sensitivity issues' do
        sensitivities = tool.send(:detect_cultural_sensitivities, content)
        
        expect(sensitivities).to be_an(Array)
        sensitivities.each do |sensitivity|
          expect(sensitivity).to have_key(:type)
          expect(sensitivity).to have_key(:severity)
          expect(sensitivity).to have_key(:description)
        end
      end
    end

    describe '#calculate_dimension_weights' do
      it 'calculates appropriate weights for authenticity dimensions' do
        weights = tool.send(:calculate_dimension_weights)
        
        expect(weights).to be_a(Hash)
        expect(weights.keys).to include(:brand_consistency, :cultural_sensitivity, :emotional_genuineness, :claim_accuracy)
        expect(weights.values.sum).to be_within(0.01).of(1.0)
      end
    end

    describe '#generate_authenticity_insights' do
      let(:dimension_results) do
        {
          brand_consistency: { score: 8.5, issues: [] },
          cultural_sensitivity: { score: 6.0, issues: ['cultural_reference'] },
          emotional_genuineness: { score: 9.0, issues: [] },
          claim_accuracy: { score: 5.5, issues: ['unverified_statistic'] }
        }
      end

      it 'generates insights from dimension analysis' do
        insights = tool.send(:generate_authenticity_insights, dimension_results)
        
        expect(insights).to be_a(Hash)
        expect(insights).to have_key(:strengths)
        expect(insights).to have_key(:weaknesses)
        expect(insights).to have_key(:opportunities)
        expect(insights).to have_key(:threats)
      end
    end
  end
end
