# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CulturalMomentTool, type: :service do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, tenant: tenant) }
  let(:tool) { described_class.new(campaign) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#initialize' do
    it 'initializes with a campaign' do
      expect(tool.campaign).to eq(campaign)
    end

    it 'raises error without campaign' do
      expect { described_class.new(nil) }.to raise_error(ArgumentError, 'Campaign is required')
    end
  end

  describe '#identify_cultural_moments' do
    it 'identifies relevant cultural moments' do
      result = tool.identify_cultural_moments
      
      expect(result).to be_a(Hash)
      expect(result).to have_key(:trending_moments)
      expect(result).to have_key(:emerging_moments)
      expect(result).to have_key(:seasonal_moments)
      expect(result).to have_key(:relevance_scores)
    end

    it 'categorizes moments by trend status' do
      result = tool.identify_cultural_moments
      
      expect(result[:trending_moments]).to be_an(Array)
      expect(result[:emerging_moments]).to be_an(Array)
      expect(result[:seasonal_moments]).to be_an(Array)
    end

    it 'includes relevance scoring' do
      result = tool.identify_cultural_moments
      relevance_scores = result[:relevance_scores]
      
      expect(relevance_scores).to be_a(Hash)
      relevance_scores.each_value do |score|
        expect(score).to be_a(Float)
        expect(score).to be_between(0, 10)
      end
    end
  end

  describe '#create_cultural_moment' do
    let(:moment_params) do
      {
        moment_name: 'Summer Wellness Trend',
        description: 'Growing focus on summer wellness and outdoor activities',
        category: 'lifestyle',
        trend_status: 'trending',
        cultural_context: { themes: ['wellness', 'outdoor', 'summer'], demographics: ['millennials', 'gen_z'] },
        engagement_metrics: { social_mentions: 15000, sentiment_score: 8.2, growth_rate: 25.5 },
        relevance_factors: { seasonality: 9.0, audience_overlap: 7.5, brand_alignment: 8.0 }
      }
    end

    it 'creates cultural moment record' do
      expect { tool.create_cultural_moment(moment_params) }.to change { CulturalMoment.count }.by(1)
    end

    it 'associates moment with tenant' do
      moment = tool.create_cultural_moment(moment_params)
      expect(moment.tenant).to eq(tenant)
    end

    it 'sets moment attributes correctly' do
      moment = tool.create_cultural_moment(moment_params)
      
      expect(moment.moment_name).to eq('Summer Wellness Trend')
      expect(moment.category).to eq('lifestyle')
      expect(moment.trend_status).to eq('trending')
      expect(moment.cultural_context['themes']).to include('wellness')
    end
  end

  describe '#trend_analysis' do
    let!(:trending_moment) { create(:cultural_moment, :trending, tenant: tenant) }
    let!(:emerging_moment) { create(:cultural_moment, trend_status: 'emerging', tenant: tenant) }

    it 'analyzes cultural trend patterns' do
      analysis = tool.trend_analysis
      
      expect(analysis).to be_a(Hash)
      expect(analysis).to have_key(:trend_overview)
      expect(analysis).to have_key(:momentum_analysis)
      expect(analysis).to have_key(:lifecycle_positions)
      expect(analysis).to have_key(:opportunity_windows)
    end

    it 'provides trend momentum analysis' do
      analysis = tool.trend_analysis
      momentum = analysis[:momentum_analysis]
      
      expect(momentum).to be_a(Hash)
      expect(momentum).to have_key(:accelerating_trends)
      expect(momentum).to have_key(:stable_trends)
      expect(momentum).to have_key(:declining_trends)
    end

    it 'identifies opportunity windows' do
      analysis = tool.trend_analysis
      opportunities = analysis[:opportunity_windows]
      
      expect(opportunities).to be_an(Array)
      opportunities.each do |window|
        expect(window).to have_key(:moment_name)
        expect(window).to have_key(:optimal_timing)
        expect(window).to have_key(:engagement_potential)
      end
    end
  end

  describe '#cultural_relevance_scoring' do
    let(:cultural_moment) { create(:cultural_moment, :trending, tenant: tenant) }

    it 'scores cultural relevance for campaign' do
      score = tool.cultural_relevance_scoring(cultural_moment)
      
      expect(score).to be_a(Hash)
      expect(score).to have_key(:overall_relevance_score)
      expect(score).to have_key(:component_scores)
      expect(score).to have_key(:alignment_factors)
      expect(score).to have_key(:opportunity_rating)
    end

    it 'breaks down component scores' do
      score = tool.cultural_relevance_scoring(cultural_moment)
      components = score[:component_scores]
      
      expect(components).to have_key(:audience_overlap)
      expect(components).to have_key(:brand_alignment)
      expect(components).to have_key(:timing_relevance)
      expect(components).to have_key(:content_synergy)
    end

    it 'calculates overall relevance score' do
      score = tool.cultural_relevance_scoring(cultural_moment)
      overall_score = score[:overall_relevance_score]
      
      expect(overall_score).to be_a(Float)
      expect(overall_score).to be_between(0, 10)
    end
  end

  describe '#moment_timing_optimization' do
    let(:cultural_moment) { create(:cultural_moment, :trending, tenant: tenant) }

    it 'optimizes timing for cultural moment engagement' do
      optimization = tool.moment_timing_optimization(cultural_moment)
      
      expect(optimization).to be_a(Hash)
      expect(optimization).to have_key(:optimal_launch_window)
      expect(optimization).to have_key(:peak_engagement_times)
      expect(optimization).to have_key(:timing_recommendations)
      expect(optimization).to have_key(:risk_assessment)
    end

    it 'identifies optimal launch window' do
      optimization = tool.moment_timing_optimization(cultural_moment)
      launch_window = optimization[:optimal_launch_window]
      
      expect(launch_window).to have_key(:start_date)
      expect(launch_window).to have_key(:end_date)
      expect(launch_window).to have_key(:confidence_level)
      expect(launch_window).to have_key(:reasoning)
    end

    it 'provides timing recommendations' do
      optimization = tool.moment_timing_optimization(cultural_moment)
      recommendations = optimization[:timing_recommendations]
      
      expect(recommendations).to be_an(Array)
      recommendations.each do |rec|
        expect(rec).to have_key(:timing_aspect)
        expect(rec).to have_key(:recommendation)
        expect(rec).to have_key(:rationale)
      end
    end
  end

  describe '#cultural_surfing_strategy' do
    it 'develops cultural surfing strategy' do
      strategy = tool.cultural_surfing_strategy
      
      expect(strategy).to be_a(Hash)
      expect(strategy).to have_key(:primary_moments)
      expect(strategy).to have_key(:secondary_moments)
      expect(strategy).to have_key(:surfing_approach)
      expect(strategy).to have_key(:content_adaptations)
      expect(strategy).to have_key(:timing_coordination)
    end

    it 'prioritizes cultural moments' do
      strategy = tool.cultural_surfing_strategy
      primary_moments = strategy[:primary_moments]
      
      expect(primary_moments).to be_an(Array)
      primary_moments.each do |moment|
        expect(moment).to have_key(:moment_name)
        expect(moment).to have_key(:priority_score)
        expect(moment).to have_key(:engagement_strategy)
      end
    end

    it 'defines surfing approach' do
      strategy = tool.cultural_surfing_strategy
      approach = strategy[:surfing_approach]
      
      expect(approach).to have_key(:strategy_type)
      expect(approach).to have_key(:aggressiveness_level)
      expect(approach).to have_key(:adaptation_frequency)
      expect(approach).to have_key(:risk_tolerance)
    end
  end

  describe '#moment_content_alignment' do
    let(:cultural_moment) { create(:cultural_moment, :trending, tenant: tenant) }

    it 'aligns content with cultural moment' do
      alignment = tool.moment_content_alignment(cultural_moment)
      
      expect(alignment).to be_a(Hash)
      expect(alignment).to have_key(:content_adjustments)
      expect(alignment).to have_key(:messaging_adaptations)
      expect(alignment).to have_key(:visual_recommendations)
      expect(alignment).to have_key(:tone_modifications)
    end

    it 'provides specific content adjustments' do
      alignment = tool.moment_content_alignment(cultural_moment)
      adjustments = alignment[:content_adjustments]
      
      expect(adjustments).to be_an(Array)
      adjustments.each do |adjustment|
        expect(adjustment).to have_key(:content_element)
        expect(adjustment).to have_key(:current_state)
        expect(adjustment).to have_key(:recommended_change)
        expect(adjustment).to have_key(:cultural_relevance)
      end
    end
  end

  describe '#audience_cultural_mapping' do
    let(:audience_data) do
      {
        demographics: { age_range: '25-34', location: 'urban', education: 'college' },
        interests: %w[technology sustainability wellness],
        cultural_values: %w[authenticity diversity innovation],
        media_consumption: %w[social_media podcasts streaming]
      }
    end

    it 'maps audience to cultural moments' do
      mapping = tool.audience_cultural_mapping(audience_data)
      
      expect(mapping).to be_a(Hash)
      expect(mapping).to have_key(:cultural_affinities)
      expect(mapping).to have_key(:moment_preferences)
      expect(mapping).to have_key(:engagement_patterns)
      expect(mapping).to have_key(:content_preferences)
    end

    it 'identifies cultural affinities' do
      mapping = tool.audience_cultural_mapping(audience_data)
      affinities = mapping[:cultural_affinities]
      
      expect(affinities).to be_a(Hash)
      expect(affinities).to have_key(:primary_cultures)
      expect(affinities).to have_key(:subcultures)
      expect(affinities).to have_key(:values_alignment)
    end
  end

  describe '#cultural_risk_assessment' do
    let(:cultural_moment) { create(:cultural_moment, :trending, tenant: tenant) }

    it 'assesses cultural engagement risks' do
      assessment = tool.cultural_risk_assessment(cultural_moment)
      
      expect(assessment).to be_a(Hash)
      expect(assessment).to have_key(:overall_risk_level)
      expect(assessment).to have_key(:risk_factors)
      expect(assessment).to have_key(:mitigation_strategies)
      expect(assessment).to have_key(:monitoring_recommendations)
    end

    it 'identifies specific risk factors' do
      assessment = tool.cultural_risk_assessment(cultural_moment)
      risk_factors = assessment[:risk_factors]
      
      expect(risk_factors).to be_an(Array)
      risk_factors.each do |factor|
        expect(factor).to have_key(:risk_type)
        expect(factor).to have_key(:severity)
        expect(factor).to have_key(:likelihood)
        expect(factor).to have_key(:description)
      end
    end

    it 'provides mitigation strategies' do
      assessment = tool.cultural_risk_assessment(cultural_moment)
      strategies = assessment[:mitigation_strategies]
      
      expect(strategies).to be_an(Array)
      strategies.each do |strategy|
        expect(strategy).to have_key(:risk_area)
        expect(strategy).to have_key(:mitigation_approach)
        expect(strategy).to have_key(:implementation_steps)
      end
    end
  end

  describe '#cultural_performance_metrics' do
    it 'defines cultural performance metrics' do
      metrics = tool.cultural_performance_metrics
      
      expect(metrics).to be_a(Hash)
      expect(metrics).to have_key(:engagement_metrics)
      expect(metrics).to have_key(:relevance_indicators)
      expect(metrics).to have_key(:cultural_impact_measures)
      expect(metrics).to have_key(:timing_effectiveness)
    end

    it 'includes specific KPIs' do
      metrics = tool.cultural_performance_metrics
      engagement = metrics[:engagement_metrics]
      
      expect(engagement).to be_a(Hash)
      expect(engagement).to have_key(:cultural_mentions)
      expect(engagement).to have_key(:moment_hashtag_usage)
      expect(engagement).to have_key(:cultural_sentiment)
    end
  end

  describe 'private methods' do
    describe '#analyze_cultural_context' do
      it 'analyzes cultural context of campaign' do
        context = tool.send(:analyze_cultural_context)
        
        expect(context).to be_a(Hash)
        expect(context).to have_key(:cultural_themes)
        expect(context).to have_key(:demographic_focus)
        expect(context).to have_key(:cultural_values)
      end
    end

    describe '#calculate_moment_relevance' do
      let(:moment) { create(:cultural_moment, :trending, tenant: tenant) }
      let(:campaign_context) { { themes: ['technology', 'innovation'], audience: ['millennials'] } }

      it 'calculates relevance score for moment' do
        relevance = tool.send(:calculate_moment_relevance, moment, campaign_context)
        
        expect(relevance).to be_a(Float)
        expect(relevance).to be_between(0, 10)
      end
    end

    describe '#identify_trending_patterns' do
      it 'identifies trending cultural patterns' do
        patterns = tool.send(:identify_trending_patterns)
        
        expect(patterns).to be_an(Array)
        patterns.each do |pattern|
          expect(pattern).to have_key(:pattern_type)
          expect(pattern).to have_key(:trend_strength)
          expect(pattern).to have_key(:growth_trajectory)
        end
      end
    end

    describe '#generate_cultural_recommendations' do
      let(:analysis_data) do
        {
          high_relevance_moments: ['sustainability_focus', 'digital_wellness'],
          timing_opportunities: ['eco_awareness_week', 'mental_health_month'],
          audience_preferences: { values: ['authenticity', 'innovation'] }
        }
      end

      it 'generates cultural moment recommendations' do
        recommendations = tool.send(:generate_cultural_recommendations, analysis_data)
        
        expect(recommendations).to be_an(Array)
        recommendations.each do |rec|
          expect(rec).to have_key(:moment_type)
          expect(rec).to have_key(:recommendation)
          expect(rec).to have_key(:timing_suggestion)
        end
      end
    end
  end
end
