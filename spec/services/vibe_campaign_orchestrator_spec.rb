# frozen_string_literal: true

require 'rails_helper'

RSpec.describe VibeCampaignOrchestrator, type: :service do
  let(:tenant) { create(:tenant) }
  let(:campaign) { create(:campaign, :with_vibe_data, tenant: tenant) }
  let(:cultural_moment) { create(:cultural_moment, :trending, tenant: tenant) }
  let(:orchestrator) { described_class.new(campaign) }

  before do
    ActsAsTenant.current_tenant = tenant
  end

  describe '#initialize' do
    it 'initializes with a campaign' do
      expect(orchestrator.campaign).to eq(campaign)
    end

    it 'raises error with invalid campaign' do
      expect { described_class.new(nil) }.to raise_error(ArgumentError, 'Campaign is required')
    end
  end

  describe '#orchestrate' do
    let(:orchestration) { create(:vibe_campaign_orchestration, :cultural_surfing, campaign: campaign, tenant: tenant) }

    before do
      allow(campaign).to receive(:vibe_campaign_orchestration).and_return(orchestration)
    end

    context 'with cultural surfing strategy' do
      it 'executes cultural surfing orchestration' do
        result = orchestrator.orchestrate

        expect(result).to be_a(Hash)
        expect(result[:success]).to be true
        expect(result[:strategy]).to eq('cultural_surfing')
        expect(result[:orchestration_id]).to eq(orchestration.id)
      end

      it 'updates orchestration status to active' do
        orchestrator.orchestrate
        expect(orchestration.reload.status).to eq('active')
      end
    end

    context 'with authenticity focused strategy' do
      let(:authenticity_orchestration) { create(:vibe_campaign_orchestration, :authenticity_focused, campaign: campaign, tenant: tenant) }

      before do
        allow(campaign).to receive(:vibe_campaign_orchestration).and_return(authenticity_orchestration)
      end

      it 'executes authenticity focused orchestration' do
        result = orchestrator.orchestrate

        expect(result[:success]).to be true
        expect(result[:strategy]).to eq('authenticity_focused')
        expect(result[:checks_performed]).to be_an(Array)
      end
    end

    context 'with performance optimization strategy' do
      let(:performance_orchestration) { create(:vibe_campaign_orchestration, :performance_optimization, campaign: campaign, tenant: tenant) }

      before do
        allow(campaign).to receive(:vibe_campaign_orchestration).and_return(performance_orchestration)
      end

      it 'executes performance optimization' do
        result = orchestrator.orchestrate

        expect(result[:success]).to be true
        expect(result[:strategy]).to eq('performance_optimization')
        expect(result[:optimizations_applied]).to be_an(Array)
      end
    end

    context 'when orchestration fails' do
      before do
        allow(orchestration).to receive(:update!).and_raise(StandardError, 'Database error')
      end

      it 'handles errors gracefully' do
        result = orchestrator.orchestrate

        expect(result[:success]).to be false
        expect(result[:error]).to include('Database error')
      end

      it 'sets orchestration status to failed' do
        orchestrator.orchestrate
        expect(orchestration.reload.status).to eq('failed')
      end
    end
  end

  describe '#cultural_alignment_score' do
    let!(:vibe_analysis) { create(:vibe_analysis_record, :cultural, campaign: campaign, tenant: tenant) }

    it 'calculates cultural alignment score' do
      score = orchestrator.cultural_alignment_score

      expect(score).to be_a(Float)
      expect(score).to be_between(0.0, 10.0)
    end

    context 'with high cultural relevance' do
      before do
        vibe_analysis.update(
          cultural_data: {
            relevance_factors: {
              trending_topics: 9.2,
              cultural_moments: 8.8,
              audience_alignment: 9.0
            }
          }
        )
      end

      it 'returns high alignment score' do
        score = orchestrator.cultural_alignment_score
        expect(score).to be > 8.0
      end
    end

    context 'with low cultural relevance' do
      before do
        vibe_analysis.update(
          cultural_data: {
            relevance_factors: {
              trending_topics: 4.2,
              cultural_moments: 3.8,
              audience_alignment: 4.0
            }
          }
        )
      end

      it 'returns low alignment score' do
        score = orchestrator.cultural_alignment_score
        expect(score).to be < 5.0
      end
    end
  end

  describe '#emotional_resonance_strength' do
    let!(:emotional_profile) { create(:emotional_resonance_profile, :high_intensity, campaign: campaign, tenant: tenant) }

    it 'calculates emotional resonance strength' do
      strength = orchestrator.emotional_resonance_strength

      expect(strength).to be_a(Float)
      expect(strength).to be_between(0.0, 10.0)
    end

    it 'considers emotional intensity distribution' do
      strength = orchestrator.emotional_resonance_strength
      expect(strength).to be > 7.0  # High intensity profile should have strong resonance
    end
  end

  describe '#authenticity_risk_level' do
    let!(:authenticity_check) { create(:authenticity_check, campaign: campaign, tenant: tenant) }

    it 'calculates authenticity risk level' do
      risk = orchestrator.authenticity_risk_level

      expect(risk).to be_a(String)
      expect(%w[low medium high]).to include(risk)
    end

    context 'with flagged authenticity check' do
      let!(:flagged_check) { create(:authenticity_check, :flagged, campaign: campaign, tenant: tenant) }

      it 'returns high risk level' do
        risk = orchestrator.authenticity_risk_level
        expect(risk).to eq('high')
      end
    end

    context 'with approved authenticity check' do
      it 'returns low risk level' do
        risk = orchestrator.authenticity_risk_level
        expect(risk).to eq('low')
      end
    end
  end

  describe '#optimization_recommendations' do
    let!(:vibe_analysis) { create(:vibe_analysis_record, campaign: campaign, tenant: tenant) }
    let!(:emotional_profile) { create(:emotional_resonance_profile, campaign: campaign, tenant: tenant) }

    it 'generates optimization recommendations' do
      recommendations = orchestrator.optimization_recommendations

      expect(recommendations).to be_an(Array)
      expect(recommendations).not_to be_empty
      expect(recommendations.first).to have_key(:type)
      expect(recommendations.first).to have_key(:description)
      expect(recommendations.first).to have_key(:priority)
    end

    it 'includes cultural timing recommendations' do
      recommendations = orchestrator.optimization_recommendations
      cultural_recs = recommendations.select { |r| r[:type] == 'cultural_timing' }
      
      expect(cultural_recs).not_to be_empty
    end

    it 'includes emotional tuning recommendations' do
      recommendations = orchestrator.optimization_recommendations
      emotional_recs = recommendations.select { |r| r[:type] == 'emotional_tuning' }
      
      expect(emotional_recs).not_to be_empty
    end
  end

  describe '#performance_metrics' do
    let(:orchestration) { create(:vibe_campaign_orchestration, campaign: campaign, tenant: tenant) }

    before do
      allow(campaign).to receive(:vibe_campaign_orchestration).and_return(orchestration)
    end

    it 'returns comprehensive performance metrics' do
      metrics = orchestrator.performance_metrics

      expect(metrics).to be_a(Hash)
      expect(metrics).to have_key(:cultural_alignment)
      expect(metrics).to have_key(:emotional_resonance)
      expect(metrics).to have_key(:authenticity_score)
      expect(metrics).to have_key(:overall_vibe_score)
    end

    it 'calculates overall vibe score' do
      metrics = orchestrator.performance_metrics
      
      expect(metrics[:overall_vibe_score]).to be_a(Float)
      expect(metrics[:overall_vibe_score]).to be_between(0.0, 10.0)
    end
  end

  describe '#strategy_effectiveness' do
    let(:orchestration) { create(:vibe_campaign_orchestration, :cultural_surfing, campaign: campaign, tenant: tenant) }

    before do
      allow(campaign).to receive(:vibe_campaign_orchestration).and_return(orchestration)
      orchestration.update(
        performance_data: {
          engagement_lift: 25.5,
          conversion_improvement: 18.2,
          cultural_moments_capitalized: 3,
          authenticity_maintained: true
        }
      )
    end

    it 'evaluates strategy effectiveness' do
      effectiveness = orchestrator.strategy_effectiveness

      expect(effectiveness).to be_a(Hash)
      expect(effectiveness).to have_key(:score)
      expect(effectiveness).to have_key(:strengths)
      expect(effectiveness).to have_key(:improvement_areas)
      expect(effectiveness[:score]).to be_between(0.0, 10.0)
    end

    it 'identifies strategy strengths' do
      effectiveness = orchestrator.strategy_effectiveness
      
      expect(effectiveness[:strengths]).to be_an(Array)
      expect(effectiveness[:strengths]).not_to be_empty
    end
  end

  describe 'private methods' do
    describe '#execute_cultural_surfing' do
      let(:orchestration) { create(:vibe_campaign_orchestration, :cultural_surfing, campaign: campaign, tenant: tenant) }

      it 'executes cultural surfing strategy' do
        result = orchestrator.send(:execute_cultural_surfing, orchestration)

        expect(result).to be_a(Hash)
        expect(result).to have_key(:cultural_moments_identified)
        expect(result).to have_key(:timing_optimizations)
        expect(result).to have_key(:audience_alignments)
      end
    end

    describe '#execute_authenticity_focus' do
      let(:orchestration) { create(:vibe_campaign_orchestration, :authenticity_focused, campaign: campaign, tenant: tenant) }

      it 'executes authenticity focused strategy' do
        result = orchestrator.send(:execute_authenticity_focus, orchestration)

        expect(result).to be_a(Hash)
        expect(result).to have_key(:authenticity_checks)
        expect(result).to have_key(:compliance_validations)
        expect(result).to have_key(:risk_mitigations)
      end
    end

    describe '#execute_performance_optimization' do
      let(:orchestration) { create(:vibe_campaign_orchestration, :performance_optimization, campaign: campaign, tenant: tenant) }

      it 'executes performance optimization strategy' do
        result = orchestrator.send(:execute_performance_optimization, orchestration)

        expect(result).to be_a(Hash)
        expect(result).to have_key(:optimization_actions)
        expect(result).to have_key(:performance_predictions)
        expect(result).to have_key(:resource_allocations)
      end
    end

    describe '#calculate_overall_vibe_score' do
      it 'calculates weighted overall vibe score' do
        cultural_score = 8.5
        emotional_score = 7.8
        authenticity_score = 9.2

        overall_score = orchestrator.send(:calculate_overall_vibe_score, cultural_score, emotional_score, authenticity_score)

        expect(overall_score).to be_a(Float)
        expect(overall_score).to be_between(0.0, 10.0)
        expect(overall_score).to be > 8.0  # Should reflect high individual scores
      end
    end

    describe '#generate_cultural_recommendations' do
      let!(:vibe_analysis) { create(:vibe_analysis_record, :cultural, campaign: campaign, tenant: tenant) }

      it 'generates cultural optimization recommendations' do
        recommendations = orchestrator.send(:generate_cultural_recommendations)

        expect(recommendations).to be_an(Array)
        expect(recommendations).not_to be_empty
        expect(recommendations.first).to include(:type, :description, :priority)
      end
    end

    describe '#generate_emotional_recommendations' do
      let!(:emotional_profile) { create(:emotional_resonance_profile, campaign: campaign, tenant: tenant) }

      it 'generates emotional tuning recommendations' do
        recommendations = orchestrator.send(:generate_emotional_recommendations)

        expect(recommendations).to be_an(Array)
        expect(recommendations).not_to be_empty
        expect(recommendations.first).to include(:type, :description, :priority)
      end
    end

    describe '#assess_strategy_performance' do
      let(:orchestration) { create(:vibe_campaign_orchestration, campaign: campaign, tenant: tenant) }

      before do
        orchestration.update(
          performance_data: {
            engagement_lift: 22.3,
            conversion_improvement: 15.8,
            cultural_alignment_score: 8.7,
            authenticity_maintenance: 95.2
          }
        )
      end

      it 'assesses strategy performance from data' do
        assessment = orchestrator.send(:assess_strategy_performance, orchestration)

        expect(assessment).to be_a(Hash)
        expect(assessment).to have_key(:effectiveness_score)
        expect(assessment).to have_key(:performance_indicators)
        expect(assessment[:effectiveness_score]).to be_between(0.0, 10.0)
      end
    end
  end

  describe 'error handling' do
    context 'when campaign has no vibe data' do
      let(:basic_campaign) { create(:campaign, tenant: tenant) }
      let(:basic_orchestrator) { described_class.new(basic_campaign) }

      it 'handles missing vibe analysis gracefully' do
        score = basic_orchestrator.cultural_alignment_score
        expect(score).to eq(0.0)
      end

      it 'handles missing emotional profile gracefully' do
        strength = basic_orchestrator.emotional_resonance_strength
        expect(strength).to eq(0.0)
      end
    end

    context 'when orchestration has invalid data' do
      let(:orchestration) { create(:vibe_campaign_orchestration, campaign: campaign, tenant: tenant) }

      before do
        allow(campaign).to receive(:vibe_campaign_orchestration).and_return(orchestration)
        orchestration.update(performance_data: { invalid: 'data' })
      end

      it 'handles invalid performance data' do
        expect { orchestrator.performance_metrics }.not_to raise_error
      end
    end
  end

  describe 'integration scenarios' do
    let!(:vibe_analysis) { create(:vibe_analysis_record, :emotional, campaign: campaign, tenant: tenant) }
    let!(:emotional_profile) { create(:emotional_resonance_profile, :high_intensity, campaign: campaign, tenant: tenant) }
    let!(:authenticity_check) { create(:authenticity_check, campaign: campaign, tenant: tenant) }
    let!(:orchestration) { create(:vibe_campaign_orchestration, :cultural_surfing, campaign: campaign, tenant: tenant) }

    before do
      allow(campaign).to receive(:vibe_campaign_orchestration).and_return(orchestration)
    end

    it 'performs complete orchestration workflow' do
      result = orchestrator.orchestrate
      metrics = orchestrator.performance_metrics
      recommendations = orchestrator.optimization_recommendations

      expect(result[:success]).to be true
      expect(metrics[:overall_vibe_score]).to be > 0
      expect(recommendations).not_to be_empty
    end

    it 'maintains data consistency across operations' do
      cultural_score = orchestrator.cultural_alignment_score
      emotional_strength = orchestrator.emotional_resonance_strength
      risk_level = orchestrator.authenticity_risk_level

      expect(cultural_score).to be_a(Float)
      expect(emotional_strength).to be_a(Float)
      expect(risk_level).to be_a(String)

      # Scores should be consistent with test data setup
      expect(cultural_score).to be > 0
      expect(emotional_strength).to be > 7.0  # High intensity profile
      expect(risk_level).to eq('low')  # Approved authenticity check
    end
  end
end
