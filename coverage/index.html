<!DOCTYPE html>
<html xmlns='http://www.w3.org/1999/xhtml'>
  <head>
    <title>Code coverage for Ai marketing hub</title>
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <script src='./assets/0.13.1/application.js' type='text/javascript'></script>
    <link href='./assets/0.13.1/application.css' media='screen, projection, print' rel='stylesheet' type='text/css' />
    <link rel="icon" type="image/png" href="./assets/0.13.1/favicon_yellow.png" />
  </head>

  <body>
    <div id="loading">
      <img src="./assets/0.13.1/loading.gif" alt="loading"/>
    </div>
    <div id="wrapper" class="hide">
      <div class="timestamp">Generated <abbr class="timeago" title="2025-06-04T19:50:57+01:00">2025-06-04T19:50:57+01:00</abbr></div>
      <ul class="group_tabs"></ul>

      <div id="content">
        <div class="file_list_container" id="AllFiles">
  <h2>
    <span class="group_name">All Files</span>
    (<span class="covered_percent">
      <span class="yellow">
  86.47%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="green">
         3.07
       </span>
    </span> hits/line
    )
  </h2>

  <a name="AllFiles"></a>

  <div>
    <b>10</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>207</b> relevant lines,
    <span class="green"><b>179</b> lines covered</span> and
    <span class="red"><b>28</b> lines missed. </span>
    (<span class="yellow">
  86.47%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f318fe844efc2e04a91767fbe5c80bd79b7b31a5" class="src_link" title="app/controllers/application_controller.rb">app/controllers/application_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a943a96d45113dadb3525abb158f5aa3097a1684" class="src_link" title="app/helpers/application_helper.rb">app/helpers/application_helper.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#c0ff159d7bfba348c48bbfefc63038a131689b54" class="src_link" title="app/jobs/application_job.rb">app/jobs/application_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#391ac91811e0a5fb27b504e4682164a5a0f8f410" class="src_link" title="app/mailers/application_mailer.rb">app/mailers/application_mailer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#fc8555b45fe71d95f0bdf2dad35a9e55e3fd0e40" class="src_link" title="app/models/application_record.rb">app/models/application_record.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">3</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#76e068ba36d367fbbb05c5509ed27dd2f54bd2bf" class="src_link" title="app/models/campaign.rb">app/models/campaign.rb</a></td>
            <td class="green strong cell--number t-file__coverage">97.62 %</td>
            <td class="cell--number">126</td>
            <td class="cell--number">42</td>
            <td class="cell--number">41</td>
            <td class="cell--number">1</td>
            <td class="cell--number">3.02</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#d891f0bcb4d739068865bd102b4019ff10739da1" class="src_link" title="app/models/email_campaign.rb">app/models/email_campaign.rb</a></td>
            <td class="yellow strong cell--number t-file__coverage">84.09 %</td>
            <td class="cell--number">124</td>
            <td class="cell--number">44</td>
            <td class="cell--number">37</td>
            <td class="cell--number">7</td>
            <td class="cell--number">1.18</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#05d822389b1fb6e4177998d25d1d7a7b7990c1c4" class="src_link" title="app/models/social_campaign.rb">app/models/social_campaign.rb</a></td>
            <td class="yellow strong cell--number t-file__coverage">88.31 %</td>
            <td class="cell--number">184</td>
            <td class="cell--number">77</td>
            <td class="cell--number">68</td>
            <td class="cell--number">9</td>
            <td class="cell--number">3.29</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#359a599972b364bfe6fbd6b069e07894a1c9b2c1" class="src_link" title="app/models/tenant.rb">app/models/tenant.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">59</td>
            <td class="cell--number">15</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0</td>
            <td class="cell--number">8.20</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#5c73d717a7153b9cbb94168d89a499ccb9ab10b1" class="src_link" title="app/models/user.rb">app/models/user.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">75</td>
            <td class="cell--number">16</td>
            <td class="cell--number">16</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4.94</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>


        
          <div class="file_list_container" id="Controllers">
  <h2>
    <span class="group_name">Controllers</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Controllers"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>3</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>3</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#f318fe844efc2e04a91767fbe5c80bd79b7b31a5" class="src_link" title="app/controllers/application_controller.rb">app/controllers/application_controller.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0</td>
            <td class="cell--number">3</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Channels">
  <h2>
    <span class="group_name">Channels</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Channels"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Models">
  <h2>
    <span class="group_name">Models</span>
    (<span class="covered_percent">
      <span class="green">
  91.33%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="green">
         3.24
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Models"></a>

  <div>
    <b>6</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>196</b> relevant lines,
    <span class="green"><b>179</b> lines covered</span> and
    <span class="red"><b>17</b> lines missed. </span>
    (<span class="green">
  91.33%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#fc8555b45fe71d95f0bdf2dad35a9e55e3fd0e40" class="src_link" title="app/models/application_record.rb">app/models/application_record.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">3</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">1.00</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#76e068ba36d367fbbb05c5509ed27dd2f54bd2bf" class="src_link" title="app/models/campaign.rb">app/models/campaign.rb</a></td>
            <td class="green strong cell--number t-file__coverage">97.62 %</td>
            <td class="cell--number">126</td>
            <td class="cell--number">42</td>
            <td class="cell--number">41</td>
            <td class="cell--number">1</td>
            <td class="cell--number">3.02</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#d891f0bcb4d739068865bd102b4019ff10739da1" class="src_link" title="app/models/email_campaign.rb">app/models/email_campaign.rb</a></td>
            <td class="yellow strong cell--number t-file__coverage">84.09 %</td>
            <td class="cell--number">124</td>
            <td class="cell--number">44</td>
            <td class="cell--number">37</td>
            <td class="cell--number">7</td>
            <td class="cell--number">1.18</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#05d822389b1fb6e4177998d25d1d7a7b7990c1c4" class="src_link" title="app/models/social_campaign.rb">app/models/social_campaign.rb</a></td>
            <td class="yellow strong cell--number t-file__coverage">88.31 %</td>
            <td class="cell--number">184</td>
            <td class="cell--number">77</td>
            <td class="cell--number">68</td>
            <td class="cell--number">9</td>
            <td class="cell--number">3.29</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#359a599972b364bfe6fbd6b069e07894a1c9b2c1" class="src_link" title="app/models/tenant.rb">app/models/tenant.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">59</td>
            <td class="cell--number">15</td>
            <td class="cell--number">15</td>
            <td class="cell--number">0</td>
            <td class="cell--number">8.20</td>
            
          </tr>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#5c73d717a7153b9cbb94168d89a499ccb9ab10b1" class="src_link" title="app/models/user.rb">app/models/user.rb</a></td>
            <td class="green strong cell--number t-file__coverage">100.00 %</td>
            <td class="cell--number">75</td>
            <td class="cell--number">16</td>
            <td class="cell--number">16</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4.94</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Mailers">
  <h2>
    <span class="group_name">Mailers</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Mailers"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>4</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>4</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#391ac91811e0a5fb27b504e4682164a5a0f8f410" class="src_link" title="app/mailers/application_mailer.rb">app/mailers/application_mailer.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">4</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0</td>
            <td class="cell--number">4</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Helpers">
  <h2>
    <span class="group_name">Helpers</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Helpers"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>2</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>2</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#a943a96d45113dadb3525abb158f5aa3097a1684" class="src_link" title="app/helpers/application_helper.rb">app/helpers/application_helper.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">2</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Jobs">
  <h2>
    <span class="group_name">Jobs</span>
    (<span class="covered_percent">
      <span class="red">
  0.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Jobs"></a>

  <div>
    <b>1</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>2</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>2</b> lines missed. </span>
    (<span class="red">
  0.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
          <tr class="t-file">
            <td class="strong t-file__name"><a href="#c0ff159d7bfba348c48bbfefc63038a131689b54" class="src_link" title="app/jobs/application_job.rb">app/jobs/application_job.rb</a></td>
            <td class="red strong cell--number t-file__coverage">0.00 %</td>
            <td class="cell--number">7</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0</td>
            <td class="cell--number">2</td>
            <td class="cell--number">0.00</td>
            
          </tr>
        
      </tbody>
    </table>
  </div>
</div>

        
          <div class="file_list_container" id="Libraries">
  <h2>
    <span class="group_name">Libraries</span>
    (<span class="covered_percent">
      <span class="green">
  100.0%
</span>

     </span>
     covered at
     <span class="covered_strength">
       <span class="red">
         0.0
       </span>
    </span> hits/line
    )
  </h2>

  <a name="Libraries"></a>

  <div>
    <b>0</b> files in total.
  </div>

  <div class="t-line-summary">
    <b>0</b> relevant lines,
    <span class="green"><b>0</b> lines covered</span> and
    <span class="red"><b>0</b> lines missed. </span>
    (<span class="green">
  100.0%
</span>
)
  </div>

  

  <div class="file_list--responsive">
    <table class="file_list">
      <thead>
        <tr>
          <th>File</th>
          <th class="cell--number">% covered</th>
          <th class="cell--number">Lines</th>
          <th class="cell--number">Relevant Lines</th>
          <th class="cell--number">Lines covered</th>
          <th class="cell--number">Lines missed</th>
          <th class="cell--number">Avg. Hits / Line</th>
          
        </tr>
      </thead>
      <tbody>
        
      </tbody>
    </table>
  </div>
</div>

        
      </div>

      <div id="footer">
        Generated by <a href="https://github.com/simplecov-ruby/simplecov">simplecov</a> v0.22.0
        and simplecov-html v0.13.1<br/>
        using RSpec
      </div>

      <div class="source_files">
      
        <div class="source_table" id="f318fe844efc2e04a91767fbe5c80bd79b7b31a5">
  <div class="header">
    <h3>app/controllers/application_controller.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>3</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>3</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationController &lt; ActionController::Base</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Only allow modern browsers supporting webp images, web push, badges, import maps, CSS nesting, and CSS :has.</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  allow_browser versions: :modern</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="a943a96d45113dadb3525abb158f5aa3097a1684">
  <div class="header">
    <h3>app/helpers/application_helper.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>2</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>2</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">module ApplicationHelper</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="c0ff159d7bfba348c48bbfefc63038a131689b54">
  <div class="header">
    <h3>app/jobs/application_job.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>2</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>2</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationJob &lt; ActiveJob::Base</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby">  # Automatically retry jobs that encountered a deadlock</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">  # retry_on ActiveRecord::Deadlocked</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">  # Most jobs are safe to ignore if the underlying records are no longer available</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">  # discard_on ActiveJob::DeserializationError</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="7">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="391ac91811e0a5fb27b504e4682164a5a0f8f410">
  <div class="header">
    <h3>app/mailers/application_mailer.rb</h3>
    <h4>
      <span class="red">
  0.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>4</b> relevant lines.
      <span class="green"><b>0</b> lines covered</span> and
      <span class="red"><b>4</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="1">
            

            

            <code class="ruby">class ApplicationMailer &lt; ActionMailer::Base</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="2">
            

            

            <code class="ruby">  default from: &quot;<EMAIL>&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="3">
            

            

            <code class="ruby">  layout &quot;mailer&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="4">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="fc8555b45fe71d95f0bdf2dad35a9e55e3fd0e40">
  <div class="header">
    <h3>app/models/application_record.rb</h3>
    <h4>
      <span class="green">
  100.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>2</b> relevant lines.
      <span class="green"><b>2</b> lines covered</span> and
      <span class="red"><b>0</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="1">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class ApplicationRecord &lt; ActiveRecord::Base</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="2">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  primary_abstract_class</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="76e068ba36d367fbbb05c5509ed27dd2f54bd2bf">
  <div class="header">
    <h3>app/models/campaign.rb</h3>
    <h4>
      <span class="green">
  97.62%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>42</b> relevant lines.
      <span class="green"><b>41</b> lines covered</span> and
      <span class="red"><b>1</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"># == Schema Information</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"># Table name: campaigns</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">#  id              :bigint           not null, primary key</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">#  name            :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby">#  description     :text</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">#  campaign_type   :integer          default(0), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby">#  status          :integer          default(0), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">#  target_audience :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">#  start_date      :date</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">#  end_date        :date</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">#  budget_cents    :integer          default(0)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">#  settings        :jsonb            default({}), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">#  tenant_id       :bigint           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby">#  created_by_id   :bigint           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby">#  created_at      :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">#  updated_at      :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"># Indexes</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby">#  index_campaigns_on_campaign_type        (campaign_type)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby">#  index_campaigns_on_created_by_id        (created_by_id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">#  index_campaigns_on_name_and_tenant_id   (name,tenant_id) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby">#  index_campaigns_on_settings             (settings) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">#  index_campaigns_on_start_date           (start_date)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby">#  index_campaigns_on_status               (status)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby">#  index_campaigns_on_tenant_id            (tenant_id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"># Foreign Keys</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">#  fk_rails_...  (created_by_id =&gt; users.id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">#  fk_rails_...  (tenant_id =&gt; tenants.id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="35">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="37">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class Campaign &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby">  # Multi-tenancy</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="39">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  acts_as_tenant(:tenant)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="40">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :tenant, required: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="41">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :created_by, class_name: &#39;User&#39;, required: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="43">
            

            

            <code class="ruby">  # Specialized campaign associations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="44">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_one :email_campaign, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="45">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_one :social_campaign, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="46">
            

            

            <code class="ruby">  # TODO: Add this when model is created</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby">  # has_one :seo_campaign, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="48">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby">  # TODO: Add metrics association when model is created</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="50">
            

            

            <code class="ruby">  # has_many :campaign_metrics, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="52">
            

            

            <code class="ruby">  # Validations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="53">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :name, presence: true, uniqueness: { scope: :tenant_id }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="54">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :campaign_type, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="55">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :status, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="56">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :target_audience, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="57">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :budget_cents, numericality: { greater_than_or_equal_to: 0 }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="58">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby">  # Date validations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="28" data-linenumber="60">
            
              <span class="hits">28</span>
            

            

            <code class="ruby">  validate :end_date_after_start_date, if: -&gt; { start_date.present? &amp;&amp; end_date.present? }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="61">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="62">
            

            

            <code class="ruby">  # Enums</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="63">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  enum :status, { </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="64">
            

            

            <code class="ruby">    draft: 0, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="65">
            

            

            <code class="ruby">    active: 1, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="66">
            

            

            <code class="ruby">    paused: 2, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="67">
            

            

            <code class="ruby">    completed: 3, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="68">
            

            

            <code class="ruby">    cancelled: 4 </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="69">
            

            

            <code class="ruby">  }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="70">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="71">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  enum :campaign_type, { </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="72">
            

            

            <code class="ruby">    email: 0, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="73">
            

            

            <code class="ruby">    social: 1, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="74">
            

            

            <code class="ruby">    seo: 2, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="75">
            

            

            <code class="ruby">    multi_channel: 3 </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="76">
            

            

            <code class="ruby">  }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="77">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="78">
            

            

            <code class="ruby">  # Scopes</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="79">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :by_status, -&gt;(status) { where(status: status) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="80">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :by_type, -&gt;(type) { where(campaign_type: type) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="81">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :recent, -&gt; { order(created_at: :desc) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="82">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :ongoing, -&gt; { where(status: [:active, :paused]) }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="83">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="84">
            

            

            <code class="ruby">  # Instance methods</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="85">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def can_be_activated?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="86">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    draft? || paused?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="87">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="88">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="89">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def duration_in_days</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="3" data-linenumber="90">
            
              <span class="hits">3</span>
            

            

            <code class="ruby">    return nil unless start_date.present? &amp;&amp; end_date.present?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="91">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    (end_date - start_date).to_i</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="92">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="93">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="94">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def progress_percentage</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="3" data-linenumber="95">
            
              <span class="hits">3</span>
            

            

            <code class="ruby">    return 100 if completed?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="96">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    return 0 if draft? || start_date.blank? || end_date.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="97">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="98">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    now = Date.current</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="99">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return 0 if now &lt; start_date</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="100">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return 100 if now &gt; end_date</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="101">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="102">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    total_days = duration_in_days</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="103">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return 0 if total_days &lt;= 0</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="104">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="105">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    elapsed_days = (now - start_date).to_i</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="106">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    ((elapsed_days.to_f / total_days) * 100).round</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="107">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="108">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="109">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def budget_in_dollars</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="110">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    budget_cents / 100.0</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="111">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="112">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="113">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def budget_in_dollars=(amount)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="114">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    self.budget_cents = (amount.to_f * 100).round</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="115">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="116">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="117">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="118">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="119">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def end_date_after_start_date</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="27" data-linenumber="120">
            
              <span class="hits">27</span>
            

            

            <code class="ruby">    return unless start_date.present? &amp;&amp; end_date.present?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="121">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="27" data-linenumber="122">
            
              <span class="hits">27</span>
            

            

            <code class="ruby">    if end_date &lt;= start_date</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="123">
            

            

            <code class="ruby">      errors.add(:end_date, &#39;must be after start date&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="124">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="125">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="126">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="d891f0bcb4d739068865bd102b4019ff10739da1">
  <div class="header">
    <h3>app/models/email_campaign.rb</h3>
    <h4>
      <span class="yellow">
  84.09%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>44</b> relevant lines.
      <span class="green"><b>37</b> lines covered</span> and
      <span class="red"><b>7</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"># == Schema Information</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"># Table name: email_campaigns</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">#  id           :bigint           not null, primary key</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">#  campaign_id  :bigint           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby">#  subject_line :string(150)      not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">#  preview_text :string(200)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby">#  content      :text             not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">#  from_name    :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">#  from_email   :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">#  settings     :jsonb            default({}), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">#  created_at   :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">#  updated_at   :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"># Indexes</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">#  index_email_campaigns_on_campaign_id  (campaign_id) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby">#  index_email_campaigns_on_from_email   (from_email)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby">#  index_email_campaigns_on_settings     (settings) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"># Foreign Keys</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">#  fk_rails_...  (campaign_id =&gt; campaigns.id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="28">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class EmailCampaign &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby">  # Associations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="30">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :campaign, required: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby">  # Validations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="33">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :subject_line, presence: true, length: { maximum: 150 }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="34">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :content, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="35">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :from_name, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="36">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :from_email, presence: true, format: { </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby">    with: URI::MailTo::EMAIL_REGEXP, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby">    message: &#39;must be a valid email address&#39; </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby">  }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="40">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :preview_text, length: { maximum: 200 }, allow_blank: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby">  # Scopes</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="43">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :ready_to_send, -&gt; { </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="44">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    joins(:campaign)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="45">
            

            

            <code class="ruby">      .where(campaigns: { status: &#39;active&#39; })</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="46">
            

            

            <code class="ruby">      .where.not(subject_line: [nil, &#39;&#39;])</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="47">
            

            

            <code class="ruby">      .where.not(content: [nil, &#39;&#39;])</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="48">
            

            

            <code class="ruby">      .where.not(from_email: [nil, &#39;&#39;])</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby">  }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="50">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby">  # Delegate tenant access through campaign</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="52">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def tenant</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="53">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    campaign.tenant</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="54">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="56">
            

            

            <code class="ruby">  # Instance methods</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="57">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def ready_to_send?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="4" data-linenumber="58">
            
              <span class="hits">4</span>
            

            

            <code class="ruby">    valid? &amp;&amp; </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby">    campaign&amp;.active? &amp;&amp; </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="60">
            

            

            <code class="ruby">    subject_line.present? &amp;&amp; </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="61">
            

            

            <code class="ruby">    content.present? &amp;&amp; </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="62">
            

            

            <code class="ruby">    from_email.present? &amp;&amp;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="63">
            

            

            <code class="ruby">    valid_email_format?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="64">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="65">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="66">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def preview_snippet(length = 100)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="67">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    return &#39;&#39; if content.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="68">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="69">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    if content.length &lt;= length</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="70">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">      content</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="71">
            

            

            <code class="ruby">    else</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="72">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">      content[0..length-1] + &#39;...&#39;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="73">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="74">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="75">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="76">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def estimated_send_time</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="77">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    recipient_count = settings.dig(&#39;recipient_count&#39;) || 0</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="78">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    send_rate_per_minute = 1000 # emails per minute</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="79">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="80">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    minutes = (recipient_count.to_f / send_rate_per_minute).ceil</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="81">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    [minutes, 1].max # minimum 1 minute</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="82">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="83">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="84">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def personalize_content_for(user_data = {})</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="85">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    personalized_content = content.dup</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="86">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="87">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    user_data.each do |key, value|</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="88">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">      personalized_content.gsub!(&quot;{{#{key}}}&quot;, value.to_s)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="89">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="90">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="91">
            

            

            <code class="ruby">    # Remove any remaining merge tags</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="92">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    personalized_content.gsub!(/\{\{[^}]+\}\}/, &#39;&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="93">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="94">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    personalized_content</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="95">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="96">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="97">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def recipient_count</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="98">
            

            

            <code class="ruby">    settings.dig(&#39;recipient_count&#39;) || 0</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="99">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="100">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="101">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def a_b_test_enabled?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="102">
            

            

            <code class="ruby">    settings.dig(&#39;a_b_test&#39;, &#39;enabled&#39;) == true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="103">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="104">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="105">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def scheduled?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="106">
            

            

            <code class="ruby">    settings.dig(&#39;delivery_options&#39;, &#39;scheduled_at&#39;).present?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="107">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="108">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="109">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def scheduled_at</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="110">
            

            

            <code class="ruby">    scheduled_time = settings.dig(&#39;delivery_options&#39;, &#39;scheduled_at&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="111">
            

            

            <code class="ruby">    return nil if scheduled_time.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="112">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="113">
            

            

            <code class="ruby">    Time.zone.parse(scheduled_time)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="114">
            

            

            <code class="ruby">  rescue ArgumentError</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="115">
            

            

            <code class="ruby">    nil</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="116">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="117">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="118">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="119">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="120">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def valid_email_format?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="121">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return false if from_email.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="122">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    from_email.match?(URI::MailTo::EMAIL_REGEXP)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="123">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="124">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="05d822389b1fb6e4177998d25d1d7a7b7990c1c4">
  <div class="header">
    <h3>app/models/social_campaign.rb</h3>
    <h4>
      <span class="yellow">
  88.31%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>77</b> relevant lines.
      <span class="green"><b>68</b> lines covered</span> and
      <span class="red"><b>9</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"># == Schema Information</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"># Table name: social_campaigns</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">#  id                   :bigint           not null, primary key</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">#  campaign_id          :bigint           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby">#  platforms            :jsonb            default([]), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">#  content_variants     :jsonb            default({}), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby">#  hashtags             :string           default(&quot;&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">#  target_demographics  :jsonb            default({}), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">#  post_schedule        :jsonb            default({}), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">#  social_settings      :jsonb            default({}), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">#  created_at           :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">#  updated_at           :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby"># Indexes</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">#  index_social_campaigns_on_campaign_id          (campaign_id) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby">#  index_social_campaigns_on_content_variants     (content_variants) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby">#  index_social_campaigns_on_platforms            (platforms) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">#  index_social_campaigns_on_post_schedule        (post_schedule) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby">#  index_social_campaigns_on_social_settings      (social_settings) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby">#  index_social_campaigns_on_target_demographics  (target_demographics) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby"># Foreign Keys</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby">#  fk_rails_...  (campaign_id =&gt; campaigns.id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="31">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class SocialCampaign &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby">  # Associations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="33">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :campaign, required: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="35">
            

            

            <code class="ruby">  # Constants</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="36">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  SUPPORTED_PLATFORMS = %w[twitter linkedin facebook instagram youtube tiktok].freeze</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="37">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  PLATFORM_LIMITS = {</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby">    &#39;twitter&#39; =&gt; 280,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby">    &#39;linkedin&#39; =&gt; 3000,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby">    &#39;facebook&#39; =&gt; 63206,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby">    &#39;instagram&#39; =&gt; 2200,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby">    &#39;youtube&#39; =&gt; 5000,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="43">
            

            

            <code class="ruby">    &#39;tiktok&#39; =&gt; 150</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby">  }.freeze</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="45">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="46">
            

            

            <code class="ruby">  # Validations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="47">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :platforms, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="48">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :content_variants, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="49">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validate :at_least_one_platform</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="50">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validate :platforms_are_supported</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="51">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validate :content_exists_for_all_platforms</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="52">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validate :content_respects_platform_limits</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="53">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="54">
            

            

            <code class="ruby">  # Scopes</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="3" data-linenumber="55">
            
              <span class="hits">3</span>
            

            

            <code class="ruby">  scope :for_platform, -&gt;(platform) { where(&quot;platforms @&gt; ?&quot;, [platform].to_json) }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="56">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  scope :ready_to_post, -&gt; { </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="57">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    joins(:campaign)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="58">
            

            

            <code class="ruby">      .where(campaigns: { status: &#39;active&#39; })</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby">      .where(&quot;jsonb_array_length(platforms) &gt; 0&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="60">
            

            

            <code class="ruby">      .where(&quot;content_variants != &#39;{}&#39;::jsonb&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="61">
            

            

            <code class="ruby">  }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="62">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="63">
            

            

            <code class="ruby">  # Delegate tenant access through campaign</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="64">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def tenant</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="65">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    campaign.tenant</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="66">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="67">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="68">
            

            

            <code class="ruby">  # Instance methods</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="69">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def supported_platforms</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="70">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    SUPPORTED_PLATFORMS</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="71">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="72">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="73">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def ready_to_post?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="3" data-linenumber="74">
            
              <span class="hits">3</span>
            

            

            <code class="ruby">    return false unless campaign&amp;.active?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="75">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return false if platforms.empty?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="76">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return false unless content_complete?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="77">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return false unless valid?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="78">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="79">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="80">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="81">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="82">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def content_for_platform(platform)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="5" data-linenumber="83">
            
              <span class="hits">5</span>
            

            

            <code class="ruby">    content_variants[platform]</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="84">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="85">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="86">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def character_count_for_platform(platform)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="87">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    content = content_for_platform(platform)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="88">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    content ? content.length : 0</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="89">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="90">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="91">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def scheduled_posts</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="92">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    post_schedule.dig(&#39;posts&#39;) || []</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="93">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="94">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="95">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def hashtag_list</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="96">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    return [] if hashtags.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="97">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="98">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    hashtags.split(/\s+/).reject(&amp;:blank?)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="99">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="100">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="101">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def estimated_reach</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="102">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    follower_counts = social_settings.dig(&#39;follower_counts&#39;) || {}</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="103">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    engagement_rate = social_settings.dig(&#39;engagement_rate&#39;) || 0.1</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="104">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="105">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    total_followers = follower_counts.values.sum</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="106">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    (total_followers * engagement_rate).round</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="107">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="108">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="109">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def platforms_text</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="110">
            

            

            <code class="ruby">    platforms.join(&#39;, &#39;).titleize</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="111">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="112">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="113">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def primary_platform</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="114">
            

            

            <code class="ruby">    platforms.first</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="115">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="116">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="117">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def multi_platform?</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="118">
            

            

            <code class="ruby">    platforms.length &gt; 1</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="119">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="120">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="121">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def add_scheduled_post(platform, content, scheduled_at)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="122">
            

            

            <code class="ruby">    current_posts = scheduled_posts</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="123">
            

            

            <code class="ruby">    current_posts &lt;&lt; {</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="124">
            

            

            <code class="ruby">      &#39;platform&#39; =&gt; platform,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="125">
            

            

            <code class="ruby">      &#39;content&#39; =&gt; content,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="126">
            

            

            <code class="ruby">      &#39;scheduled_at&#39; =&gt; scheduled_at.iso8601</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="127">
            

            

            <code class="ruby">    }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="128">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="129">
            

            

            <code class="ruby">    self.post_schedule = post_schedule.merge(&#39;posts&#39; =&gt; current_posts)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="130">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="131">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="132">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def remove_scheduled_post(index)</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="133">
            

            

            <code class="ruby">    current_posts = scheduled_posts</code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="134">
            

            

            <code class="ruby">    current_posts.delete_at(index) if index &lt; current_posts.length</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="135">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="missed" data-hits="0" data-linenumber="136">
            

            

            <code class="ruby">    self.post_schedule = post_schedule.merge(&#39;posts&#39; =&gt; current_posts)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="137">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="138">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="139">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="140">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="141">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def at_least_one_platform</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="15" data-linenumber="142">
            
              <span class="hits">15</span>
            

            

            <code class="ruby">    if platforms.blank? || platforms.empty?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="3" data-linenumber="143">
            
              <span class="hits">3</span>
            

            

            <code class="ruby">      errors.add(:platforms, &#39;must include at least one platform&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="144">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="145">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="146">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="147">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def platforms_are_supported</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="15" data-linenumber="148">
            
              <span class="hits">15</span>
            

            

            <code class="ruby">    return if platforms.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="149">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="12" data-linenumber="150">
            
              <span class="hits">12</span>
            

            

            <code class="ruby">    unsupported = platforms - SUPPORTED_PLATFORMS</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="12" data-linenumber="151">
            
              <span class="hits">12</span>
            

            

            <code class="ruby">    if unsupported.any?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="152">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">      errors.add(:platforms, &#39;contains unsupported platforms&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="153">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="154">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="155">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="156">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def content_exists_for_all_platforms</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="15" data-linenumber="157">
            
              <span class="hits">15</span>
            

            

            <code class="ruby">    return if platforms.blank? || content_variants.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="158">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="11" data-linenumber="159">
            
              <span class="hits">11</span>
            

            

            <code class="ruby">    missing_content = platforms - content_variants.keys</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="11" data-linenumber="160">
            
              <span class="hits">11</span>
            

            

            <code class="ruby">    if missing_content.any?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="161">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">      errors.add(:content_variants, &#39;must have content for all selected platforms&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="162">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="163">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="164">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="165">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def content_respects_platform_limits</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="15" data-linenumber="166">
            
              <span class="hits">15</span>
            

            

            <code class="ruby">    return if content_variants.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="167">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="13" data-linenumber="168">
            
              <span class="hits">13</span>
            

            

            <code class="ruby">    content_variants.each do |platform, content|</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="19" data-linenumber="169">
            
              <span class="hits">19</span>
            

            

            <code class="ruby">      next unless PLATFORM_LIMITS[platform]</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="19" data-linenumber="170">
            
              <span class="hits">19</span>
            

            

            <code class="ruby">      next if content.blank?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="171">
            

            

            <code class="ruby">      </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="19" data-linenumber="172">
            
              <span class="hits">19</span>
            

            

            <code class="ruby">      if content.length &gt; PLATFORM_LIMITS[platform]</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="173">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">        errors.add(:content_variants, &quot;#{platform.titleize} content exceeds #{PLATFORM_LIMITS[platform]} character limit&quot;)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="174">
            

            

            <code class="ruby">      end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="175">
            

            

            <code class="ruby">    end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="176">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="177">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="178">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def content_complete?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="179">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return false if platforms.empty?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="180">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    return false if content_variants.empty?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="181">
            

            

            <code class="ruby">    </code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="182">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    platforms.all? { |platform| content_variants[platform].present? }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="183">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="184">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="359a599972b364bfe6fbd6b069e07894a1c9b2c1">
  <div class="header">
    <h3>app/models/tenant.rb</h3>
    <h4>
      <span class="green">
  100.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>15</b> relevant lines.
      <span class="green"><b>15</b> lines covered</span> and
      <span class="red"><b>0</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"># == Schema Information</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"># Table name: tenants</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">#  id         :bigint           not null, primary key</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">#  name       :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby">#  subdomain  :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">#  status     :string           default(&quot;active&quot;), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby">#  settings   :jsonb            default({}), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">#  created_at :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">#  updated_at :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby"># Indexes</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">#  index_tenants_on_name       (name) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby">#  index_tenants_on_settings   (settings) USING gin</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby">#  index_tenants_on_status     (status)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">#  index_tenants_on_subdomain  (subdomain) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="22">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class Tenant &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby">  # Associations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="24">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_many :users, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="25">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  has_many :campaigns, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby">  # TODO: Add these associations when models are created</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">  # has_many :data_sources, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby">  # has_many :agent_workflows, dependent: :destroy</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby">  # Validations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="31">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :name, presence: true, uniqueness: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="32">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :subdomain, presence: true, uniqueness: true,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">            format: { </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">              with: /\A[a-z0-9\-]+\z/, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="35">
            

            

            <code class="ruby">              message: &#39;must be lowercase alphanumeric with dashes only&#39; </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="36">
            

            

            <code class="ruby">            }</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="37">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :status, presence: true, inclusion: { in: %w[active suspended cancelled] }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="38">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby">  # Enums</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="40">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  enum :status, { active: &#39;active&#39;, suspended: &#39;suspended&#39;, cancelled: &#39;cancelled&#39; }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="42">
            

            

            <code class="ruby">  # Callbacks</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="43">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  before_validation :normalize_subdomain</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="45">
            

            

            <code class="ruby">  # Instance methods</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="46">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def active?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="2" data-linenumber="47">
            
              <span class="hits">2</span>
            

            

            <code class="ruby">    status == &#39;active&#39;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="48">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="50">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def full_domain(host = &#39;localhost:3000&#39;)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="51">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    &quot;#{subdomain}.#{host}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="52">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="53">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="54">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="55">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="56">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def normalize_subdomain</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="108" data-linenumber="57">
            
              <span class="hits">108</span>
            

            

            <code class="ruby">    self.subdomain = subdomain&amp;.downcase&amp;.strip</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="58">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
        <div class="source_table" id="5c73d717a7153b9cbb94168d89a499ccb9ab10b1">
  <div class="header">
    <h3>app/models/user.rb</h3>
    <h4>
      <span class="green">
  100.0%
</span>

      lines covered
    </h4>

    

    <div class="t-line-summary">
      <b>16</b> relevant lines.
      <span class="green"><b>16</b> lines covered</span> and
      <span class="red"><b>0</b> lines missed.</span>
    </div>

    

  </div>

  <pre>
    <ol>
      
        <div>
          <li class="never" data-hits="" data-linenumber="1">
            

            

            <code class="ruby"># frozen_string_literal: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="2">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="3">
            

            

            <code class="ruby"># == Schema Information</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="4">
            

            

            <code class="ruby"># Table name: users</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="5">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="6">
            

            

            <code class="ruby">#  id                     :bigint           not null, primary key</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="7">
            

            

            <code class="ruby">#  email                  :string           default(&quot;&quot;), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="8">
            

            

            <code class="ruby">#  encrypted_password     :string           default(&quot;&quot;), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="9">
            

            

            <code class="ruby">#  reset_password_token   :string</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="10">
            

            

            <code class="ruby">#  reset_password_sent_at :datetime</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="11">
            

            

            <code class="ruby">#  remember_created_at    :datetime</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="12">
            

            

            <code class="ruby">#  confirmation_token     :string</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="13">
            

            

            <code class="ruby">#  confirmed_at           :datetime</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="14">
            

            

            <code class="ruby">#  confirmation_sent_at   :datetime</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="15">
            

            

            <code class="ruby">#  unconfirmed_email      :string</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="16">
            

            

            <code class="ruby">#  tenant_id              :bigint           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="17">
            

            

            <code class="ruby">#  first_name             :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="18">
            

            

            <code class="ruby">#  last_name              :string           not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="19">
            

            

            <code class="ruby">#  role                   :integer          default(0), not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="20">
            

            

            <code class="ruby">#  created_at             :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="21">
            

            

            <code class="ruby">#  updated_at             :datetime         not null</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="22">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="23">
            

            

            <code class="ruby"># Indexes</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="24">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="25">
            

            

            <code class="ruby">#  index_users_on_confirmation_token         (confirmation_token) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="26">
            

            

            <code class="ruby">#  index_users_on_email_and_tenant_id        (email,tenant_id) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="27">
            

            

            <code class="ruby">#  index_users_on_reset_password_token       (reset_password_token) UNIQUE</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="28">
            

            

            <code class="ruby">#  index_users_on_role                       (role)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="29">
            

            

            <code class="ruby">#  index_users_on_tenant_id                  (tenant_id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="30">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="31">
            

            

            <code class="ruby"># Foreign Keys</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="32">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="33">
            

            

            <code class="ruby">#  fk_rails_...  (tenant_id =&gt; tenants.id)</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="34">
            

            

            <code class="ruby">#</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="35">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="36">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">class User &lt; ApplicationRecord</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="37">
            

            

            <code class="ruby">  # Devise modules - using custom validations instead of :validatable</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="38">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  devise :database_authenticatable, :registerable,</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="39">
            

            

            <code class="ruby">         :recoverable, :rememberable, :confirmable</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="40">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="41">
            

            

            <code class="ruby">  # Multi-tenancy</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="42">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  acts_as_tenant(:tenant)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="43">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  belongs_to :tenant, required: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="44">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="45">
            

            

            <code class="ruby">  # Validations</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="46">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :first_name, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="47">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :last_name, presence: true</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="48">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="49">
            

            

            <code class="ruby">  # Email validations (replacing Devise&#39;s :validatable)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="50">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :email, presence: true, </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="51">
            

            

            <code class="ruby">                   uniqueness: { scope: :tenant_id, case_sensitive: false },</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="52">
            

            

            <code class="ruby">                   format: { with: URI::MailTo::EMAIL_REGEXP }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="53">
            

            

            <code class="ruby">  </code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="54">
            

            

            <code class="ruby">  # Password validations (replacing Devise&#39;s :validatable)</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="55">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  validates :password, presence: true, length: { minimum: 6 }, if: :password_required?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="56">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="57">
            

            

            <code class="ruby">  # Enums - using integer mapping for better performance</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="58">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  enum :role, { member: 0, admin: 1, owner: 2 }</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="59">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="60">
            

            

            <code class="ruby">  # Instance methods</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="61">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def full_name</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="62">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">    &quot;#{first_name} #{last_name}&quot;</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="63">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="64">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="65">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def admin_or_owner?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="3" data-linenumber="66">
            
              <span class="hits">3</span>
            

            

            <code class="ruby">    admin? || owner?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="67">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="68">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="69">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  private</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="70">
            

            

            <code class="ruby"></code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="71">
            

            

            <code class="ruby">  # Required for password validation</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="1" data-linenumber="72">
            
              <span class="hits">1</span>
            

            

            <code class="ruby">  def password_required?</code>
          </li>
        </div>
      
        <div>
          <li class="covered" data-hits="62" data-linenumber="73">
            
              <span class="hits">62</span>
            

            

            <code class="ruby">    !persisted? || !password.nil? || !password_confirmation.nil?</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="74">
            

            

            <code class="ruby">  end</code>
          </li>
        </div>
      
        <div>
          <li class="never" data-hits="" data-linenumber="75">
            

            

            <code class="ruby">end</code>
          </li>
        </div>
      
    </ol>
  </pre>
</div>

      
      </div>
    </div>
  </body>
</html>
