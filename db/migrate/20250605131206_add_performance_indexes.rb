class AddPerformanceIndexes < ActiveRecord::Migration[8.0]
  def change
    # Composite indexes for common query patterns

    # Campaigns table optimizations
    add_index :campaigns, [:tenant_id, :status],
              name: 'index_campaigns_on_tenant_and_status'

    add_index :campaigns, [:tenant_id, :campaign_type],
              name: 'index_campaigns_on_tenant_and_type'

    add_index :campaigns, [:tenant_id, :created_at],
              name: 'index_campaigns_on_tenant_and_created_at'

    add_index :campaigns, [:status, :start_date],
              name: 'index_campaigns_on_status_and_start_date'

    add_index :campaigns, [:tenant_id, :status, :campaign_type],
              name: 'index_campaigns_on_tenant_status_type'

    # Campaign metrics optimizations for date range queries
    add_index :campaign_metrics, [:campaign_id, :metric_date, :impressions],
              name: 'index_campaign_metrics_performance'

    add_index :campaign_metrics, [:metric_date, :campaign_id],
              name: 'index_campaign_metrics_date_campaign'

    # Full-text search optimization for campaigns
    add_index :campaigns, "to_tsvector('english', coalesce(name, '') || ' ' || coalesce(description, '') || ' ' || coalesce(target_audience, ''))",
              using: :gin,
              name: 'index_campaigns_on_search_text'

    # Email campaigns optimization
    add_index :email_campaigns, [:campaign_id, :from_email],
              name: 'index_email_campaigns_campaign_from'

    # Social campaigns optimization for platform queries
    add_index :social_campaigns, [:campaign_id],
              where: "jsonb_array_length(platforms) > 0",
              name: 'index_social_campaigns_with_platforms'

    # Users table optimization for tenant-based queries
    add_index :users, [:tenant_id, :role],
              name: 'index_users_on_tenant_and_role'

    add_index :users, [:tenant_id, :created_at],
              name: 'index_users_on_tenant_and_created_at'
  end
end
