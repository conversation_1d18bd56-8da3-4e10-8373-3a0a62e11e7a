class CreateTenants < ActiveRecord::Migration[8.0]
  def change
    create_table :tenants do |t|
      t.string :name, null: false
      t.string :subdomain, null: false
      t.string :status, null: false, default: 'active'
      t.jsonb :settings, null: false, default: {}

      t.timestamps
    end
    
    add_index :tenants, :name, unique: true
    add_index :tenants, :subdomain, unique: true
    add_index :tenants, :status
    add_index :tenants, :settings, using: :gin
  end
end
