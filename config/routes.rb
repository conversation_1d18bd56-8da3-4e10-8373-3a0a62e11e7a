Rails.application.routes.draw do
  # Devise routes for authentication with custom controllers
  devise_for :users, controllers: {
    sessions: 'users/sessions'
  }
  
  # Root route - Landing page for non-authenticated users
  root "marketing#index"
  
  # Marketing pages
  get "features", to: "marketing#features"
  get "pricing", to: "marketing#pricing"
  get "about", to: "marketing#about"
  get "contact", to: "marketing#contact"
  
  # Authenticated dashboard
  get "dashboard", to: "dashboard#index"
  
  # RESTful campaign routes with nested agent workflows
  resources :campaigns do
    member do
      patch :activate
      patch :pause
      patch :complete
    end
    
    # Nested agent workflows for AI automation
    resources :agent_workflows, except: [:edit, :update, :destroy] do
      member do
        patch :cancel
        patch :retry
      end
      
      collection do
        post :generate_content
        post :bulk_generate
        post :optimize_campaign
      end
    end
  end
  
  # API routes for real-time updates
  namespace :api do
    namespace :v1 do
      resources :campaigns, only: [:index, :show] do
        member do
          get :metrics
          get :progress
        end
      end
    end
  end

  # Health check
  get "up" => "rails/health#show", as: :rails_health_check

  # PWA files
  # get "manifest" => "rails/pwa#manifest", as: :pwa_manifest
  # get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
end
